/* eslint-disable camelcase */
import faker from 'faker';
import { v4 as uuid } from 'uuid';
import nock, { cleanAll as clearNockScopes } from 'nock';
import sinon from 'sinon';
import { expect } from 'chai';
import request from 'supertest';
import moment from 'moment';
import { Op } from 'sequelize';
import config from 'config';
import router from '../../src';
import { app, auth } from '../helper';
import { generateAuth } from '../helpers/basic_authorization';
import UserFixtureFactory from '../fixtures/user';
import httpFixture from '../fixtures/http/user';
import {
  User as Model,
  Subscription,
  SubscriptionUsage,
  User,
  UserSetting,
} from '../../src/models';
import { client as promisePayClient } from '../../src/lib/backends/promisepay/client';
import { Backend } from '../../src/models/transaction';
import { client } from '../../src/lib/backends/stripe';
import { STRIPE_WEBHOOK_EVENTS } from '../../src/validations/constants';
import { JournalName, NormalBalance } from '../../src/models/journal_v2';
import { CurrencyCodes } from '../../src/types/currency_codes';
import { JournalTransaction } from '../../src/lib/journal_v2/journal_v2';
import * as holdFunds from '../../src/actions/hold_funds_in_wallet_action';
import * as revertHeldFunds from '../../src/actions/revert_held_funds_action';
import { systemConfig } from '../../src/system_config';

const agent = request.agent(router);

describe('/v1/users', () => {
  let fixture;
  let sandbox;
  let subscriptionsStub: sinon.SinonStub;
  let usersStub: sinon.SinonStub;
  let paymentMethodStub: sinon.SinonStub;
  let stripeSubscriptionId: string;
  let paymentMethodId: string;
  let stripeCustomerId: string;

  before('create a user', async () => {
    fixture = await (await UserFixtureFactory()).instance();
    await (await UserFixtureFactory()).instance();
  });

  beforeEach(async () => {
    sandbox = sinon.createSandbox();
    stripeSubscriptionId = uuid();
    paymentMethodId = uuid();
    stripeCustomerId = uuid();

    subscriptionsStub = sandbox
      .stub(client.subscriptions, 'retrieve')
      .resolves({
        id: stripeSubscriptionId,
        customer: fixture.stripe_customer_id,
        status: 'active',
        items: {
          data: [
            {
              id: 'si_Iy2bIaYGiEHS8D',
              price: {
                id: 'price_1IM2eSLBe5QiNtlmgCtXyq9B',
                billing_scheme: 'per_unit',
                product: {
                  id: 'prod_Ixyb39ueg4tc0W',
                  metadata: {
                    type: 'public',
                    quota: 2,
                  },
                },
                recurring: {
                  interval: 'month',
                },
                unit_amount: 30,
              },
              subscription: 'sub_Iy2bLduYqNN1RI',
            },
          ],
        },
      });

    usersStub = sandbox.stub(client.customers, 'retrieve').resolves({
      id: fixture.stripe_customer_id,
      invoice_settings: {
        default_payment_method: paymentMethodId,
      },
    });

    paymentMethodStub = sandbox
      .stub(client.paymentMethods, 'retrieve')
      .resolves({
        id: paymentMethodId,
        card: {
          brand: 'mastercard',
          last4: '4444',
        },
        billing_details: {
          name: fixture.first_name,
        },
      });
  });

  afterEach(() => {
    sandbox.restore();
    clearNockScopes();
    systemConfig.DOUBLE_ENTRY_JOURNAL = config.get<boolean>(
      'DOUBLE_ENTRY_JOURNAL'
    );
    // revert backend guard to original state
    systemConfig.SKIP_BACKENDS = true;
  });

  describe('GET', () => {
    it('must return a 200', async () => {
      const response = await app.get('/v1/users').set('Authorization', auth);
      expect(response.status).to.equal(200);
    });

    it('must return an array', async () => {
      const response = await app.get('/v1/users').set('Authorization', auth);
      expect(Array.isArray(response.body));
    });

    it('must return a user', async () => {
      const response = await app.get('/v1/users').set('Authorization', auth);
      expect(response.body.length).to.be.gt(0);
    });

    it('must limit users', async () => {
      const response = await app
        .get('/v1/users')
        .query({ per_page: 2 })
        .set('Authorization', auth);
      expect(response.body.length).to.equal(2);
    });

    it('must order users', async () => {
      const response = await app
        .get('/v1/users')
        .query({ sort: '-id' })
        .set('Authorization', auth);
      const copied = Object.assign([], response.body);
      // One day I will remember that JS sort is in place
      // _before_ I spend 5 minutes debugging this test.
      // One day...
      const sorted = copied.sort((a, b) => {
        if (a.id < b.id) return 1;
        if (b.id < a.id) return -1;
        return 0;
      });
      expect(response.body).to.eql(sorted);
    });

    describe('times', () => {
      let backstop;

      before(async () => {
        // It should always take longer than 1MS to get from the outer before to here
        // If this is flaky, though, add a 1MS wait here before test body
        const newer = await (await UserFixtureFactory()).instance();
        // @ts-expect-error - testing scenario where created_at is undefined
        const timestamp = new Date(newer.created_at);
        timestamp.setMilliseconds(timestamp.getMilliseconds() - 1);

        backstop = timestamp.toISOString();
      });

      it('must respect created_since', async () => {
        const response = await app
          .get('/v1/users')
          .query({
            created_since: backstop,
          })
          .set('Authorization', auth);
        expect(response.body.length).to.gte(1);
      });

      it('must respect updated_since', async () => {
        const response = await app
          .get('/v1/users')
          .query({
            updated_since: backstop,
          })
          .set('Authorization', auth);
        expect(response.body.length).to.gte(1);
      });
    });
  });

  describe('GET/:id', () => {
    it('should get the right user', async () => {
      const response = await app
        .get(`/v1/users/${fixture.id}`)
        .set('Authorization', auth);
      expect(response.body.id).to.equal(fixture.id);
    });

    it('should return 404 for a missing user', async () => {
      const response = await app
        .get(`/v1/users/${uuid()}`)
        .set('Authorization', auth);
      expect(response.status).to.equal(404);
    });
  });

  describe('GET/:id/subscriptions', () => {
    it("should get the user's subscriptions", async () => {
      const now = moment();
      const currentPeriodEnd = now.add(30, 'd').unix();

      const payload = {
        type: STRIPE_WEBHOOK_EVENTS.CUSTOMER_SUBSCRIPTION_CREATED,
        data: {
          object: {
            id: stripeSubscriptionId,
            status: 'active',
            customer: fixture.stripe_customer_id,
            current_period_end: currentPeriodEnd,
          },
        },
      };

      await agent
        .post('/v1/subscriptions/webhook')
        .send(payload)
        .set('Authorization', generateAuth());

      const res = await agent
        .get(`/v1/users/${fixture.id}/subscriptions`)
        .set('Authorization', generateAuth());

      expect(res.body).to.exist;

      const subscriptions = res.body;
      const subscription = subscriptions[0];

      expect(subscription.status).to.equal('active');
      expect(subscription.currentPeriodEnd).to.equal(
        moment.unix(currentPeriodEnd).toISOString()
      );
      expect(subscriptionsStub.called).to.be.true;
      expect(usersStub.called).to.be.true;
      expect(paymentMethodStub.called).to.be.true;
    });
  });

  describe('GET/:id/subscriptions/usage', () => {
    it("should return the status of the user's subscription usage", async () => {
      const user = await (
        await UserFixtureFactory({
          ordermentum_id: uuid(),
          stripe_customer_id: stripeCustomerId,
        })
      ).instance();
      const now = moment();
      const currentPeriodEnd = now.add(1, 'month').unix();

      const webhookPayload = {
        type: STRIPE_WEBHOOK_EVENTS.CUSTOMER_SUBSCRIPTION_CREATED,
        data: {
          object: {
            id: stripeSubscriptionId,
            status: 'active',
            customer: user.stripe_customer_id,
            current_period_end: currentPeriodEnd,
          },
        },
      };

      await agent
        .post('/v1/subscriptions/webhook')
        .send(webhookPayload)
        .set('Authorization', generateAuth());

      const usagePayloadOne = {
        timestamp: moment().toISOString(),
        type: 'invoice-sync',
        metadata: {
          invoiceId: uuid(),
        },
      };

      await agent
        .post(`/v1/users/${user.id}/subscriptions/usage`)
        .send(usagePayloadOne)
        .set('Authorization', generateAuth());

      const result = await Subscription.findOne({
        where: {
          stripe_subscription_id: stripeSubscriptionId,
        },
        include: [{ model: SubscriptionUsage, as: 'usages' }],
      });

      expect(result?.usages?.length).to.equal(1);

      const responseOne = await agent
        .get(`/v1/users/${user.id}/subscriptions/usage`)
        .set('Authorization', generateAuth());
      expect(responseOne.body.usage).to.equal(1);
      expect(responseOne.body.quota).to.equal(2);
      expect(responseOne.body.consumed).to.equal(false);

      const usagePayloadTwo = {
        timestamp: moment().toISOString(),
        type: 'invoice-sync',
        metadata: {
          invoiceId: uuid(),
        },
      };

      await agent
        .post(`/v1/users/${user.id}/subscriptions/usage`)
        .send(usagePayloadTwo)
        .set('Authorization', generateAuth());

      const responseTwo = await agent
        .get(`/v1/users/${user.id}/subscriptions/usage`)
        .set('Authorization', generateAuth());

      expect(responseTwo.body.usage).to.equal(2);
      expect(responseTwo.body.quota).to.equal(2);
      expect(responseTwo.body.consumed).to.equal(true);
    });
  });

  describe('POST', () => {
    it('should reject an incomplete body with a 400', async () => {
      const response = await app
        .post('/v1/users')
        .send({})
        .set('Authorization', auth);
      expect(response.status).to.equal(400);
    });

    it('should reject a body with an invalid id', async () => {
      const fixtureData = await httpFixture.user();
      fixtureData.id = 'oh hai';
      const response = await app
        .post('/v1/users')
        .send(fixtureData)
        .set('Authorization', auth);
      expect(response.status).to.equal(400);
    });

    it('should accept a valid body', async () => {
      const fixtureData = await httpFixture.user();
      const response = await app
        .post('/v1/users')
        .send(fixtureData)
        .set('Authorization', auth);
      expect(response.status).to.equal(200);
    });

    it('should create or update user settings when creating a new user', async () => {
      const fixtureData = await httpFixture.user();
      const settings = {
        settlementDelay: 5,
        manualDisbursement: true,
        supplier: false,
        netSettlement: true,
        reference: 123,
        settlementRate: 0.5,
        weeklySettlement: true,
        companyDescriptor: 'Test Company',
      };
      fixtureData.configuration = settings;

      const response = await app
        .post('/v1/users')
        .send(fixtureData)
        .set('Authorization', auth);

      expect(response.status).to.equal(200);

      const userSetting = await UserSetting.findOne({
        where: { userId: response.body.id },
      });
      expect(userSetting).to.exist;
      expect(userSetting?.settlementDelayDays).to.equal(
        settings.settlementDelay
      );
      expect(userSetting?.manualDisbursement).to.equal(
        settings.manualDisbursement
      );
      expect(userSetting?.isSupplier).to.equal(settings.supplier);
      expect(userSetting?.netSettlement).to.equal(settings.netSettlement);
      expect(userSetting?.reference).to.equal(settings.reference);
      expect(userSetting?.settlementRate).to.equal(
        String(settings.settlementRate)
      );
      expect(userSetting?.weeklySettlement).to.equal(settings.weeklySettlement);
      expect(userSetting?.companyDescriptor).to.equal(
        settings.companyDescriptor
      );
    });

    it('should return the persisted data', async () => {
      const fixtureData = await httpFixture.user();
      const response = await app
        .post('/v1/users')
        .send(fixtureData)
        .set('Authorization', auth);
      expect(response.body.name).to.eql(fixtureData.name);
      expect(response.body.email).to.eql(fixtureData.email);
    });

    it('must return a 400 for invalid data in supplier user creation', async () => {
      const fixtureData = await httpFixture.user();
      fixtureData.dob = '123';
      const res = await app
        .post('/v1/users')
        .send(fixtureData)
        .set('Authorization', auth);

      expect(res.status).to.equal(400);
    });

    it('must return a 200 for empty dob data in supplier user creation', async () => {
      const fixtureData = await httpFixture.user();
      // @ts-expect-error - testing scenario where dob is undefined
      fixtureData.dob = undefined;
      const res = await app
        .post('/v1/users')
        .send(fixtureData)
        .set('Authorization', auth);

      expect(res.status).to.equal(200);
    });

    it('must return a 200 for empty mobile number in supplier user creation', async () => {
      const fixtureData = await httpFixture.user();
      fixtureData.mobile_number = undefined;
      const res = await app
        .post('/v1/users')
        .send(fixtureData)
        .set('Authorization', auth);
      expect(res.status).to.equal(200);
    });

    it('should create retailer user', async () => {
      const fixtureData = await httpFixture.user();
      fixtureData.settlement_webhook = '';
      // @ts-expect-error - testing scenario where dob is undefined
      fixtureData.dob = undefined;
      fixtureData.mobile_number = null;
      const response = await app
        .post('/v1/users')
        .send(fixtureData)
        .set('Authorization', auth);
      expect(response.status).to.equal(200);
    });

    it('should create a customer in stripe alongside other backends', async () => {
      const fixtureData = await httpFixture.user();
      systemConfig.SKIP_BACKENDS = false;
      // stub promisepay calls
      const backendCreateUserStub: sinon.SinonStub = sandbox
        .stub(promisePayClient.users, 'createUser')
        .resolves();
      sandbox.stub(promisePayClient.companies, 'createCompany').resolves();

      // mock stripe calls
      const retrieveCustomerScope = nock('https://api.stripe.com')
        .get(
          `/v1/customers/search?query=metadata[%27ordermentumId%27]%3A%27${fixtureData.ordermentum_id}%27`
        )
        .reply(200, {
          total_count: 0,
          data: [],
        });

      const stripeCustomerId = `cus_${uuid()}`;
      const stripeCustomerCreateScope = nock('https://api.stripe.com')
        .post('/v1/customers')
        .reply(200, {
          id: stripeCustomerId,
        });

      fixtureData.settlement_webhook = '';
      // @ts-expect-error - testing scenario where dob is undefined
      fixtureData.dob = undefined;
      fixtureData.mobile_number = null;
      const response = await app
        .post('/v1/users')
        .send(fixtureData)
        .set('Authorization', auth);

      expect(response.status).to.equal(200);
      // called other backend customer creation
      expect(backendCreateUserStub.called).to.be.true;

      // create a customer in stripe
      expect(stripeCustomerCreateScope.isDone()).to.be.true;
      expect(response.body.stripe_customer_id).to.equal(stripeCustomerId);

      // called customer retrieve
      expect(retrieveCustomerScope.isDone()).to.be.true;
    });

    it('should create journals', async () => {
      const fixtureData = await httpFixture.user();
      fixtureData.settlement_webhook = '';
      // @ts-expect-error - testing scenario where dob is undefined
      fixtureData.dob = undefined;
      fixtureData.mobile_number = null;
      const response = await app
        .post('/v1/users')
        .send(fixtureData)
        .set('Authorization', auth);
      expect(response.status).to.equal(200);
      const journals = await JournalTransaction.Journal.findAll({
        where: {
          accountId: response.body.id,
        },
      });
      expect(journals.map(j => j.name)).to.have.members([
        JournalName.FEES,
        JournalName.WALLET,
      ]);
    });
  });

  describe('POST /:id/subscriptions/usage', () => {
    it("It should increment the user's active subscription usage", async () => {
      const user = await (
        await UserFixtureFactory({
          ordermentum_id: uuid(),
          stripe_customer_id: stripeCustomerId,
        })
      ).instance();
      const now = moment();
      const currentPeriodEnd = now.add(30, 'd').unix();
      const invoiceId = uuid();

      const webhookPayload = {
        type: STRIPE_WEBHOOK_EVENTS.CUSTOMER_SUBSCRIPTION_CREATED,
        data: {
          object: {
            id: stripeSubscriptionId,
            status: 'active',
            customer: user.stripe_customer_id,
            current_period_end: currentPeriodEnd,
          },
        },
      };

      await agent
        .post('/v1/subscriptions/webhook')
        .send(webhookPayload)
        .set('Authorization', generateAuth());

      const result = await Subscription.findOne({
        where: {
          stripe_subscription_id: stripeSubscriptionId,
        },
        include: [{ model: SubscriptionUsage, as: 'usages' }],
      });

      expect(result?.usages?.length).to.equal(0);

      const usagePayload = {
        timestamp: moment().toISOString(),
        type: 'invoice-sync',
        metadata: {
          invoiceId,
        },
      };

      await agent
        .post(`/v1/users/${user.id}/subscriptions/usage`)
        .send(usagePayload)
        .set('Authorization', generateAuth());

      expect(subscriptionsStub.called).to.be.true;

      const resultAfter = await Subscription.findOne({
        where: {
          stripe_subscription_id: stripeSubscriptionId,
        },
        include: [{ model: SubscriptionUsage, as: 'usages' }],
      });

      expect(resultAfter?.usages?.length).to.equal(1);
    });
  });

  describe('PUT', () => {
    beforeEach('create a user', async () => {
      const fixtureData = await httpFixture.user();
      const response = await app
        .post('/v1/users')
        .send(fixtureData)
        .set('Authorization', auth);
      fixture = response.body;
    });

    it('should reject an incomplete body with a 400', async () => {
      const response = await app
        .put(`/v1/users/${fixture.id}`)
        .send({
          id: fixture.id,
        })
        .set('Authorization', auth);

      expect(response.status).to.equal(400);
    });

    it('should accept a valid body', async () => {
      fixture.name.first = faker.name.findName();
      const response = await app
        .put(`/v1/users/${fixture.id}`)
        .send(fixture)
        .set('Authorization', auth);
      expect(response.status).to.equal(200);
    });

    it('should return the persisted data', async () => {
      fixture.name.first = faker.name.findName();
      const response = await app
        .put(`/v1/users/${fixture.id}`)
        .send(fixture)
        .set('Authorization', auth);
      expect(response.status).to.equal(200);
      expect(response.body.name).to.eql(fixture.name);
      expect(response.body.email).to.eql(fixture.email);
    });

    it('should update user settings when updating a user', async () => {
      const settings = {
        settlementDelay: 5,
        manualDisbursement: true,
        supplier: false,
        netSettlement: true,
        reference: 123,
        settlementRate: 0.5,
        weeklySettlement: true,
        companyDescriptor: 'Updated Company',
      };
      fixture.configuration = settings;

      const response = await app
        .put(`/v1/users/${fixture.id}`)
        .send(fixture)
        .set('Authorization', auth);

      expect(response.status).to.equal(200);

      const userSetting = await UserSetting.findOne({
        where: { userId: fixture.id },
      });
      expect(userSetting).to.exist;
      expect(userSetting?.settlementDelayDays).to.equal(
        settings.settlementDelay
      );
      expect(userSetting?.manualDisbursement).to.equal(
        settings.manualDisbursement
      );
      expect(userSetting?.isSupplier).to.equal(settings.supplier);
      expect(userSetting?.netSettlement).to.equal(settings.netSettlement);
      expect(userSetting?.reference).to.equal(settings.reference);
      expect(userSetting?.settlementRate).to.equal(
        String(settings.settlementRate)
      );
      expect(userSetting?.weeklySettlement).to.equal(settings.weeklySettlement);
      expect(userSetting?.companyDescriptor).to.equal(
        settings.companyDescriptor
      );
    });

    it('should actually update the database', async () => {
      fixture.name.first = faker.name.findName();
      const response = await app
        .put(`/v1/users/${fixture.id}`)
        .send(fixture)
        .set('Authorization', auth);
      const actual = await Model.findByPk(fixture.id);
      if (!actual) {
        throw new Error('Fail');
      }
      expect(response.body.name).to.eql(Model.toBody(actual).name);
    });

    it('should actually update settings', async () => {
      fixture.configuration = { companyDescriptor: 'TEST' };
      await User.update(
        {
          configuration: {
            manualDisbursement: true,
          },
        },
        {
          where: {
            id: fixture.id,
          },
        }
      );

      await app
        .put(`/v1/users/${fixture.id}`)
        .send(fixture)
        .set('Authorization', auth);

      const actual = await Model.findByPk(fixture.id);
      if (!actual) {
        throw new Error('Fail');
      }

      const user = await User.findByPk(fixture.id);
      expect(user?.configuration.companyDescriptor).to.eql(
        fixture.configuration.companyDescriptor
      );
      expect(user?.configuration.manualDisbursement).to.be.true;
    });

    it('changes backend to another and deletes all jobs containing that user id if the backend is stripe', async () => {
      fixture.configuration = {
        manualDisbursement: true,
      };

      await app
        .put(`/v1/users/${fixture.id}`)
        .send(fixture)
        .set('Authorization', auth);
      fixture.backend = Backend.STRIPE;
      await app
        .put(`/v1/users/${fixture.id}`)
        .send(fixture)
        .set('Authorization', auth);
      const actual = await Model.findByPk(fixture.id);
      if (!actual) {
        throw new Error('Fail');
      }
      expect(actual.backend).to.eql(Backend.STRIPE);
    });

    it('should return 404 for a missing user', async () => {
      const fakeId = uuid();
      fixture.id = fakeId;
      const response = await app
        .put(`/v1/users/${fakeId}`)
        .send(fixture)
        .set('Authorization', auth);
      expect(response.status).to.equal(404);
    });

    it('creates journals for users that do not have journals', async () => {
      fixture.name.first = faker.name.findName();
      const response = await app
        .put(`/v1/users/${fixture.id}`)
        .send(fixture)
        .set('Authorization', auth);
      const actual = await Model.findByPk(fixture.id);
      if (!actual) {
        throw new Error('Fail');
      }
      expect(response.body.name).to.eql(Model.toBody(actual).name);
      // creates journals for the newly created/updated user
      const journals = await JournalTransaction.Journal.findAll({
        where: {
          accountId: response.body.id,
        },
      });
      expect(journals.map(j => j.name)).to.have.members([
        JournalName.FEES,
        JournalName.WALLET,
      ]);
    });
  });

  describe('Merge', () => {
    it('should deactivate archived retailers', async () => {
      const retailer1 = await (await UserFixtureFactory()).instance();
      const retailer2 = await (await UserFixtureFactory()).instance();
      const retailer3 = await (await UserFixtureFactory()).instance();
      const updatedRetailer3 = {
        ...retailer3,
        first_name: faker.name.firstName(),
        last_name: faker.name.lastName(),
      };

      await app
        .post('/v1/users/merge')
        .send({
          archivedRetailerIds: [
            retailer1.ordermentum_id,
            retailer2.ordermentum_id,
          ],
          // @ts-expect-error - testing scenario where dob is undefined
          electedRetailer: Model.toBody(updatedRetailer3),
        })
        .set('Authorization', auth);

      const archived = await Model.findAll({
        where: {
          ordermentum_id: [retailer1.ordermentum_id, retailer2.ordermentum_id],
          status: 'archived',
        },
      });

      const updated = await Model.findOne({
        where: {
          ordermentum_id: retailer3.ordermentum_id,
          status: 'active',
        },
      });

      expect(archived.length).to.equal(2);
      expect(updated?.first_name).to.equal(updatedRetailer3.first_name);

      // creates journals for the newly created/updated user
      let journals = await JournalTransaction.Journal.findAll({
        where: {
          accountId: updated?.id,
        },
      });
      expect(journals.map(j => j.name)).to.have.members([
        JournalName.FEES,
        JournalName.WALLET,
      ]);

      journals = await JournalTransaction.Journal.findAll({
        where: {
          accountId: {
            [Op.in]: [retailer1.ordermentum_id, retailer2.ordermentum_id],
          },
        },
      });
      expect(journals.length).to.eqls(0);
    });
  });

  describe('Funding', () => {
    beforeEach('create a user', async () => {
      const fixtureData = await httpFixture.user();
      const response = await app
        .post('/v1/users')
        .send(fixtureData)
        .set('Authorization', auth);
      fixture = response.body;
    });

    it('returns a 404 when it cannot find a user against the id', async () => {
      const response = await app
        .get(`/v1/users/${uuid()}/funding`)
        .set('Authorization', auth);

      expect(response.status).to.equal(404);
    });

    it('returns funding disabled for users that do not have it enabled', async () => {
      const response = await app
        .get(`/v1/users/${fixture.id}/funding`)
        .set('Authorization', auth);

      expect(response.body.enabled).to.be.false;
    });

    it('returns funding disabled for users that have funding enabled but do not have a journal configured by the system', async () => {
      await User.update(
        {
          configuration: {
            ...fixture.configuration,
            funding: {
              enabled: true,
              purchaserIds: [],
            },
          },
        },
        {
          where: {
            id: fixture.id,
          },
        }
      );
      const response = await app
        .get(`/v1/users/${fixture.id}/funding`)
        .set('Authorization', auth);

      expect(response.body.enabled).to.be.false;
    });

    it('returns limit and available even if funding is disabled', async () => {
      await User.update(
        {
          configuration: {
            ...fixture.configuration,
            funding: {
              enabled: false,
              purchaserIds: [],
            },
          },
        },
        {
          where: {
            id: fixture.id,
          },
        }
      );

      // Give the user a limit of $1000
      const fundingLimit = 100000;
      // Available funding of $500
      const fundingAvailable = 50000;
      await JournalTransaction.Journal.create({
        name: JournalName.FUNDS,
        balanceConstraints: {
          available: {
            lte: fundingLimit,
          },
        },
        accountId: fixture.id,
        normalBalance: NormalBalance.DEBIT,
        currency: CurrencyCodes.AUD,
        balances: {
          available: {
            amount: fundingAvailable,
            credits: fundingAvailable,
            debits: 0,
          },
          pending: {
            amount: 0,
            credits: 0,
            debits: 0,
          },
        },
      });

      const response = await app
        .get(`/v1/users/${fixture.id}/funding`)
        .set('Authorization', auth);

      expect(response.body.enabled).to.be.false;
      expect(response.body.limit).to.eqls(fundingLimit);
      expect(response.body.available).to.eqls(fundingAvailable);
    });

    it('finds the available balance on the funds journal of the user that has funding enabled', async () => {
      await User.update(
        {
          configuration: {
            ...fixture.configuration,
            funding: {
              enabled: true,
              purchaserIds: [],
            },
          },
        },
        {
          where: {
            id: fixture.id,
          },
        }
      );

      // Give the user a limit of $1000
      const fundingLimit = 100000;
      // Available funding of $500
      const fundingAvailable = 50000;
      await JournalTransaction.Journal.create({
        name: JournalName.FUNDS,
        balanceConstraints: {
          available: {
            lte: fundingLimit,
          },
        },
        accountId: fixture.id,
        normalBalance: NormalBalance.DEBIT,
        currency: CurrencyCodes.AUD,
        balances: {
          available: {
            amount: fundingAvailable,
            credits: fundingAvailable,
            debits: 0,
          },
          pending: {
            amount: 0,
            credits: 0,
            debits: 0,
          },
        },
      });

      const response = await app
        .get(`/v1/users/${fixture.id}/funding`)
        .set('Authorization', auth);

      expect(response.body.enabled).to.be.true;
      expect(response.body.available).to.eqls(fundingAvailable);
      expect(response.body.limit).to.eqls(fundingLimit);
    });
  });

  describe('update funding', () => {
    beforeEach('create a user', async () => {
      const fixtureData = await httpFixture.user();
      const response = await app
        .post('/v1/users')
        .send({
          ...fixtureData,
          configuration: {
            funding: {
              enabled: false,
            },
          },
        })
        .set('Authorization', auth);
      fixture = response.body;
    });

    it('returns 400 for invalid payload', async () => {
      const response = await app
        .patch(`/v1/users/${fixture.id}/update-funding`)
        .set('Authorization', auth)
        .send({
          test: 'test',
        });
      expect(response.statusCode).to.eqls(400);
    });

    it('returns 404 if user is not found', async () => {
      const response = await app
        .patch(`/v1/users/${uuid()}/update-funding`)
        .set('Authorization', auth)
        .send({
          enabled: false,
        });
      expect(response.statusCode).to.eqls(404);
    });

    it('update funding for a valid payload', async () => {
      const original = await User.findByPk(fixture.id);
      expect(original?.configuration?.funding?.enabled).to.eql(false);

      const response = await app
        .patch(`/v1/users/${fixture.id}/update-funding`)
        .set('Authorization', auth)
        .send({
          enabled: true,
        });
      expect(response.statusCode).to.eqls(200);

      const updated = await User.findByPk(fixture.id);
      expect(updated?.configuration?.funding?.enabled).to.eql(true);
    });
  });

  describe('POST /:id/hold-funds', () => {
    let holdFundsStub: sinon.SinonStub;
    beforeEach('create a user', async () => {
      const fixtureData = await httpFixture.user();
      const response = await app
        .post('/v1/users')
        .send(fixtureData)
        .set('Authorization', auth);
      fixture = response.body;
    });

    [
      {},
      {
        amount: -1,
        description: 'test',
        userId: uuid(),
        paymentMethodId: uuid(),
        orderIds: [],
      },
      {
        amount: 1000,
        description: '',
        userId: uuid(),
        paymentMethodId: uuid(),
        orderIds: [],
      },
      {
        amount: 1000,
        description: 'test',
        userId: 'test',
        paymentMethodId: uuid(),
        orderIds: [],
      },
      {
        amount: 1000,
        description: 'test',
        userId: uuid(),
        paymentMethodId: 'test',
      },
    ].forEach(payload => {
      it('returns 400 for invalid payloads', async () => {
        const response = await app
          .post(`/v1/users/${fixture.id}/hold-funds`)
          .set('Authorization', auth)
          .send(payload);
        expect(response.statusCode).to.eqls(400);
      });
    });

    it("returns a 400 when user's provider isn't promisepay", async () => {
      await User.update(
        {
          backend: Backend.STRIPE,
        },
        {
          where: {
            id: fixture.id,
          },
        }
      );
      const response = await app
        .post(`/v1/users/${fixture.id}/hold-funds`)
        .set('Authorization', auth)
        .send({
          amount: 1000,
          description: 'test',
          paymentMethodId: uuid(),
        });

      expect(response.statusCode).to.eqls(400);
    });

    it('holds funds for a valid payload', async () => {
      const transactionId = uuid();
      holdFundsStub = sandbox
        .stub(holdFunds, 'holdFundsInWalletAction')
        .resolves({
          id: transactionId,
        });
      const response = await app
        .post(`/v1/users/${fixture.id}/hold-funds`)
        .set('Authorization', auth)
        .send({
          amount: '1.32',
          description: 'test',
          paymentMethodId: uuid(),
          orderIds: [],
        });

      expect(response.statusCode).to.eqls(200);
      expect(response.body.id).to.equal(transactionId);
      expect(holdFundsStub.called).to.be.true;
      expect(holdFundsStub.args[0][0].amount).to.eqls('1.32');
    });

    it('holds funds for a valid payload using finstro', async () => {
      holdFundsStub = sandbox
        .stub(holdFunds, 'holdFundsInWalletAction')
        .resolves();
      const response = await app
        .post(`/v1/users/${fixture.id}/hold-funds`)
        .set('Authorization', auth)
        .send({
          amount: '1.32',
          description: 'test',
          paymentMethodId: uuid(),
          orderIds: [],
        });

      expect(response.statusCode).to.eqls(200);
      expect(holdFundsStub.called).to.be.true;
      expect(holdFundsStub.args[0][0].amount).to.eqls('1.32');
    });
  });

  describe('POST v2/:id/hold-funds', () => {
    let holdFundsStub: sinon.SinonStub;
    beforeEach('create a user', async () => {
      const fixtureData = await httpFixture.user();
      const response = await app
        .post('/v1/users')
        .send(fixtureData)
        .set('Authorization', auth);
      fixture = response.body;
    });

    [
      // Empty payload
      {},
      // Negative amount
      {
        amount: -1,
        description: 'test',
        userId: uuid(),
        paymentMethodId: uuid(),
        orderIds: [],
      },
      // OrderIds is empty
      {
        amount: 1000,
        description: '',
        userId: uuid(),
        paymentMethodId: uuid(),
        orderIds: [],
      },
      // Invalid userId
      {
        amount: 1000,
        description: 'test',
        userId: 'test',
        paymentMethodId: uuid(),
        orderIds: [],
      },
      // Missing orderIds
      {
        amount: 1000,
        description: 'test',
        userId: uuid(),
        paymentMethodId: 'test',
      },
    ].forEach(payload => {
      it('returns 400 for invalid payloads', async () => {
        const response = await app
          .post(`/v2/users/${fixture.id}/hold-funds`)
          .set('Authorization', auth)
          .send(payload);
        expect(response.statusCode).to.eqls(400);
      });
    });

    it("returns a 400 when user's provider isn't promisepay", async () => {
      await User.update(
        {
          backend: Backend.STRIPE,
        },
        {
          where: {
            id: fixture.id,
          },
        }
      );
      const response = await app
        .post(`/v2/users/${fixture.id}/hold-funds`)
        .set('Authorization', auth)
        .send({
          amount: 1000,
          description: 'test',
          paymentMethodId: uuid(),
        });

      expect(response.statusCode).to.eqls(400);
    });

    it('holds funds for a valid payload', async () => {
      holdFundsStub = sandbox
        .stub(holdFunds, 'holdFundsInWalletAction')
        .resolves();
      const response = await app
        .post(`/v2/users/${fixture.id}/hold-funds`)
        .set('Authorization', auth)
        .send({
          amount: '1.32',
          description: 'test',
          paymentMethodId: uuid(),
          orderIds: [],
        });

      expect(response.statusCode).to.eqls(200);
      expect(holdFundsStub.called).to.be.true;
      expect(holdFundsStub.args[0][0].amount).to.eqls('1.32');
    });
  });

  describe('POST /:id/revert-held-funds', () => {
    let revertHeldFundsStub: sinon.SinonStub;
    let user;
    beforeEach('create a user', async () => {
      user = await httpFixture.user();
      const response = await app
        .post('/v1/users')
        .send(user)
        .set('Authorization', auth);
      fixture = response.body;
    });

    [
      {},
      {
        amount: -1,
        description: 'test',
        userId: uuid(),
      },
      {
        description: '',
        userId: uuid(),
      },
    ].forEach(payload => {
      it('returns 400 for invalid payloads', async () => {
        const response = await app
          .post(`/v1/users/${fixture.id}/revert-held-funds`)
          .set('Authorization', auth)
          .send(payload);
        expect(response.statusCode).to.eqls(400);
      });
    });

    it("returns a 400 when user's provider isn't promisepay", async () => {
      await User.update(
        {
          backend: Backend.STRIPE,
        },
        {
          where: {
            id: fixture.id,
          },
        }
      );
      const response = await app
        .post(`/v1/users/${fixture.id}/revert-held-funds`)
        .set('Authorization', auth)
        .send({
          amount: '1',
          description: 'test',
          documentId: uuid(),
        });

      expect(response.statusCode).to.eqls(400);
    });

    [
      {
        amount: '1',
        description: 'test',
        documentId: uuid(),
      },
      {
        amount: '',
        description: 'test',
        documentId: uuid(),
      },
      {
        amount: null,
        description: 'test',
        documentId: uuid(),
      },
      {
        amount: '1',
        description: 'test',
        documentId: [uuid()],
      },
    ].forEach(payload => {
      it('withdraws funds', async () => {
        revertHeldFundsStub = sandbox
          .stub(revertHeldFunds, 'revertHeldFundsAction')
          .resolves();
        const response = await app
          .post(`/v1/users/${fixture.id}/revert-held-funds`)
          .set('Authorization', auth)
          .send(payload);
        expect(response.statusCode).to.eqls(200);
        expect(revertHeldFundsStub.called).to.be.true;
      });
    });
  });

  describe('GET /:id/balance', () => {
    it("should get the user's balance", async () => {
      const res = await agent
        .get(`/v1/users/${fixture.id}/balance`)
        .set('Authorization', generateAuth());

      expect(res.body).to.exist;
      expect(res.body.balance).to.equal(0);
    });

    it("should throw 404 if user doesn't exist", async () => {
      const res = await agent
        .get(`/v1/users/${uuid()}/balance`)
        .set('Authorization', generateAuth());

      expect(res.status).to.equal(404);
    });

    it('should throw unauthorized if valid basic auth not passed', async () => {
      const res = await agent
        .get(`/v1/users/${fixture.id}/balance`)
        .set('Authorization', 'Basic nonsense');

      expect(res.status).to.equal(401);
      expect(res.body.error).to.equal(
        'Must provide both username and password'
      );
    });
  });
});
