import sinon, { SinonSandbox } from 'sinon';
import { expect } from 'chai';
import {
  zaiItemFixture,
  disburseFixture,
  zaiFundTransferGatewayFixture,
} from '../../fixtures/zai.fixture';

describe('ZaiFundTransferGateway', () => {
  let sandbox: SinonSandbox;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('should create a zai fund transfer gateway', () => {
    const fundGateway = zaiFundTransferGatewayFixture(sandbox);

    expect(fundGateway).not.to.equal(undefined);
  });

  describe('createPaymentItem', () => {
    it('should create a payment item successfully', async () => {
      // ARRANGE
      const item = zaiItemFixture();
      const { zaiFundsGateway, zaiStubs } = zaiFundTransferGatewayFixture(
        sandbox,
        {
          items: {
            createItem: sandbox.stub().resolves({
              items: item,
            }),
          },
        }
      );

      // ACT
      const result = await zaiFundsGateway.getOrCreateItem(item);

      // ASSERT
      expect(result).to.equal(item.id);
      expect(zaiStubs.items?.createItem.calledOnce).to.be.true;
    });

    it('should throw error when item creation fails', async () => {
      // ARRANGE
      const item = zaiItemFixture();
      const { zaiFundsGateway } = zaiFundTransferGatewayFixture(sandbox, {
        items: {
          createItem: sandbox.stub().rejects(new Error('Zai item not created')),
        },
      });

      // ACT & ASSERT
      return zaiFundsGateway
        .getOrCreateItem(item)
        .then(() => {
          throw new Error('Zai DID create item');
        })
        .catch(error => {
          expect(error.message).to.equal('Zai item not created');
        });
    });

    it('should handle 422 error from showItem call on zai and proceed to create item', async () => {
      // ARRANGE
      const item = zaiItemFixture();
      const { zaiFundsGateway, zaiStubs } = zaiFundTransferGatewayFixture(
        sandbox,
        {
          items: {
            showItem: sandbox.stub().rejects({
              response: {
                status: 422,
              },
              data: {
                errors: {
                  base: ['Item not found'],
                },
              },
            }),
            createItem: sandbox.stub().resolves({
              items: { id: item.id },
            }),
          },
        }
      );

      // ACT
      const result = await zaiFundsGateway.getOrCreateItem(item);

      // ASSERT
      expect(result).to.equal(item.id);
      expect(zaiStubs.items?.showItem.calledOnce).to.be.true;
      expect(zaiStubs.items?.createItem.calledOnce).to.be.true;
      expect(zaiStubs.items?.createItem.firstCall.args[0]).to.deep.equal({
        id: item.id,
        name: item.name,
        amount: item.amount,
        currency: item.currency,
        payment_type: 2,
        buyer_id: item.externalBuyerId,
        seller_id: item.externalSellerId,
        fee_ids: item.feeIds,
        description: item.description,
        tax_invoice: item.taxInvoice,
        custom_descriptor: item.customDescriptor,
      });
    });

    it('should throw non-422 errors from showItem call on zai', async () => {
      // ARRANGE
      const item = zaiItemFixture();
      const { zaiFundsGateway } = zaiFundTransferGatewayFixture(sandbox, {
        items: {
          showItem: sandbox.stub().rejects({
            response: {
              status: 500,
            },
            data: {
              errors: {
                base: ['Internal server error'],
              },
            },
          }),
        },
      });

      // ACT & ASSERT
      return zaiFundsGateway
        .getOrCreateItem(item)
        .then(() => {
          throw new Error('Should have thrown error');
        })
        .catch(error => {
          expect(error.response.status).to.equal(500);
        });
    });
  });

  describe('makePayment', () => {
    it('should make a payment successfully', async () => {
      // ARRANGE
      const item = zaiItemFixture();
      const { zaiFundsGateway, zaiStubs } = zaiFundTransferGatewayFixture(
        sandbox,
        {
          items: {
            makePayment: sandbox.stub().resolves({}),
          },
        }
      );

      // ACT
      await zaiFundsGateway.makePayment(item.id, 'backend123', '**********');

      // ASSERT
      expect(zaiStubs.items?.makePayment.calledOnceWith(item.id)).to.be.true;
    });
  });

  describe('disburseFundsToBankAccount', () => {
    it('should disburse funds successfully', async () => {
      // ARRANGE
      const payload = disburseFixture();
      const { zaiFundsGateway, zaiStubs } = zaiFundTransferGatewayFixture(
        sandbox,
        {
          walletAccounts: {
            withdrawFunds: sandbox.stub().resolves({
              disbursements: { id: 'disb123' },
            }),
          },
        }
      );

      // ACT
      await zaiFundsGateway.disburse(payload);

      // ASSERT
      expect(
        zaiStubs.walletAccounts?.withdrawFunds.calledOnceWith(
          payload.fromWallet.accountId,
          {
            amount: payload.amount,
            custom_descriptor: payload.descriptor,
            account_id: payload.toBankAccount.bankAccountId,
          }
        )
      ).to.eq(true);
    });

    it('should throw error when amount exceeds wallet balance', async () => {
      // ARRANGE
      const payload = disburseFixture({
        fromWallet: { accountId: 'wallet123', balance: 50 },
        amount: 100,
      });
      const { zaiFundsGateway } = zaiFundTransferGatewayFixture(sandbox, {
        walletAccounts: {
          withdrawFunds: sandbox
            .stub()
            .rejects(new Error('Zai disbursement failed')),
        },
      });

      // ACT & ASSERT
      await zaiFundsGateway
        .disburse(payload)
        .then(() => {
          throw new Error('Zai DID disburse');
        })
        .catch(error => {
          expect(error.message).to.equal('Invalid amount');
        });
    });

    it('should throw error when disbursement fails', async () => {
      // ARRANGE
      const payload = disburseFixture();
      const { zaiFundsGateway } = zaiFundTransferGatewayFixture(sandbox, {
        walletAccounts: {
          withdrawFunds: sandbox
            .stub()
            .rejects(new Error('Zai disbursement failed')),
        },
      });

      // ACT & ASSERT
      await zaiFundsGateway
        .disburse(payload)
        .then(() => {
          throw new Error('Zai DID disburse');
        })
        .catch(error => {
          expect(error.message).to.equal('Zai disbursement failed');
        });
    });
  });
});
