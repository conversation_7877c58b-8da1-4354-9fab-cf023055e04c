import { createSandbox, SinonSandbox } from 'sinon';
import { createTestWorker } from '@ordermentum/temporal';
import { TestWorkflowEnvironment } from '@temporalio/testing';
import { ApplicationFailure } from '@temporalio/common';
import { randomUUID } from 'crypto';
import { expect } from '../../../setup';
import promotionalPaymentWorkflowDefinition from '../../../../src/ng/workflow/definitions/promotional_payment.workflow';
import { PromotionalPaymentWorkflowArgs } from '../../../../src/ng/workflow/types/promotional_payment.types';
import { promoWorkflowArgsFactory } from '../fixtures/promotional_payment.fixtures';
import { WorkflowActivities } from '../../../../src/ng/workflow/activities.factory';

describe('PromotionalPaymentWorkflow', () => {
  let testEnv: TestWorkflowEnvironment;
  let sandbox: SinonSandbox;

  /**
   * Starts a worker and returns the workflow handle.
   * @param activities - The activities to use for the worker.
   * @param args - The arguments to pass to the workflow.
   * @returns The workflow handle.
   */
  async function startWorker(
    activities: Partial<WorkflowActivities>,
    args: PromotionalPaymentWorkflowArgs
  ) {
    const taskQueue = `test-queue-${randomUUID()}`;
    const worker = await createTestWorker({
      connection: testEnv.nativeConnection,
      taskQueue,
      logLevel: 'ERROR',
      workflowDefinition: promotionalPaymentWorkflowDefinition,
      activities,
    });
    const handle = await testEnv.client.workflow.start(
      promotionalPaymentWorkflowDefinition.name,
      {
        args: [args],
        taskQueue,
        workflowId: `workflow-id-${randomUUID()}`,
        retry: {
          maximumAttempts: 1,
          backoffCoefficient: 1,
          initialInterval: '100ms',
        },
      }
    );

    return { handle, worker };
  }

  before(async () => {
    testEnv = await TestWorkflowEnvironment.createLocal();
    sandbox = createSandbox();
  });

  afterEach(async () => {
    sandbox.restore();
  });

  after(async () => {
    await testEnv.teardown();
  });

  describe('Step 1: Check wallet balance', () => {
    it('should fail the workflow when wallet has insufficient funds', async () => {
      // ARRANGE
      const args = promoWorkflowArgsFactory.build({
        promoAmountCents: 1000,
      });

      const walletCheckBalanceStub = sandbox
        .stub()
        .rejects(
          ApplicationFailure.nonRetryable(
            'Insufficient funds',
            'InsufficientFunds'
          )
        );

      const { handle, worker } = await startWorker(
        {
          walletCheckBalance: walletCheckBalanceStub,
          walletTransfer: sandbox.stub().resolves(),
          paymentMethodTransfer: sandbox.stub().resolves({
            journalId: undefined,
            returned: {},
          }),
          promoReturnSponsoredFunds: sandbox.stub().resolves(),
          promoSucceeded: sandbox.stub().resolves(),
          promoFailed: sandbox.stub().resolves(),
        },
        args
      );

      // ACT & ASSERT
      await worker.runUntil(async () => {
        try {
          await handle.result();
          expect.fail('Expected workflow to throw error');
        } catch (error) {
          expect(error.message).to.include('Workflow execution failed');
          expect(error.cause?.failure?.cause?.message).eq('Insufficient funds');
        }
      });

      expect(walletCheckBalanceStub).to.have.been.calledOnceWith({
        walletId: args.fundingWalletId,
        requiredAmount: args.promoAmountCents,
      });
    });
  });

  describe('Step 2: Transfer promo funds', () => {
    it('should fail the workflow when wallet transfer fails', async () => {
      // ARRANGE
      const args = promoWorkflowArgsFactory.build({
        promoAmountCents: 1000,
      });

      const walletCheckBalanceStub = sandbox.stub().resolves();
      const walletTransferStub = sandbox
        .stub()
        .rejects(
          ApplicationFailure.nonRetryable(
            'Wallet transfer failed',
            'WalletTransferFailed'
          )
        );
      const promoFailedStub = sandbox.stub().resolves();

      const { handle, worker } = await startWorker(
        {
          walletCheckBalance: walletCheckBalanceStub,
          walletTransfer: walletTransferStub,
          paymentMethodTransfer: sandbox.stub().resolves({
            journalId: undefined,
            returned: {},
          }),
          promoReturnSponsoredFunds: sandbox.stub().resolves(),
          promoSucceeded: sandbox.stub().resolves(),
          promoFailed: promoFailedStub,
        },
        args
      );

      // ACT & ASSERT
      await worker.runUntil(async () => {
        try {
          await handle.result();
          expect.fail('Expected workflow to throw error');
        } catch (error) {
          expect(error.message).to.include('Workflow execution failed');
        }
      });

      expect(walletCheckBalanceStub).to.have.been.calledOnce;
      expect(walletTransferStub).to.have.been.calledOnce;
      expect(promoFailedStub).to.have.been.calledOnceWith(args);
    });

    it('should proceed to payment step when transfer succeeds', async () => {
      // ARRANGE
      const args = promoWorkflowArgsFactory.build({
        promoAmountCents: 1000,
      });

      const walletCheckBalanceStub = sandbox.stub().resolves();
      const walletTransferStub = sandbox.stub().resolves();
      const paymentMethodTransferStub = sandbox.stub().resolves({
        journalId: undefined,
        returned: {},
      });
      const promoSucceededStub = sandbox.stub().resolves();

      const { handle, worker } = await startWorker(
        {
          walletCheckBalance: walletCheckBalanceStub,
          walletTransfer: walletTransferStub,
          paymentMethodTransfer: paymentMethodTransferStub,
          promoReturnSponsoredFunds: sandbox.stub().resolves(),
          promoSucceeded: promoSucceededStub,
          promoFailed: sandbox.stub().resolves(),
        },
        args
      );

      // ACT
      await worker.runUntil(async () => handle.result());

      // ASSERT
      expect(walletCheckBalanceStub).to.have.been.calledOnce;
      expect(walletTransferStub).to.have.been.calledOnce;
      expect(paymentMethodTransferStub).to.have.been.calledOnce;
      expect(promoSucceededStub).to.have.been.calledOnceWith(args);
    });
  });

  describe('Step 3: Take payment from venue', () => {
    it('should return promo funds and fail when payment fails', async () => {
      // ARRANGE
      const args = promoWorkflowArgsFactory.build({
        promoAmountCents: 1000,
      });

      const walletCheckBalanceStub = sandbox.stub().resolves();
      const walletTransferStub = sandbox.stub().resolves();
      const paymentMethodTransferStub = sandbox
        .stub()
        .rejects(
          ApplicationFailure.nonRetryable('Payment failed', 'PaymentFailed')
        );
      const promoReturnSponsoredFundsStub = sandbox.stub().resolves();
      const promoFailedStub = sandbox.stub().resolves();

      const { handle, worker } = await startWorker(
        {
          walletCheckBalance: walletCheckBalanceStub,
          walletTransfer: walletTransferStub,
          paymentMethodTransfer: paymentMethodTransferStub,
          promoReturnSponsoredFunds: promoReturnSponsoredFundsStub,
          promoSucceeded: sandbox.stub().resolves(),
          promoFailed: promoFailedStub,
        },
        args
      );

      // ACT & ASSERT
      await worker.runUntil(async () => {
        try {
          await handle.result();
          expect.fail('Expected workflow to throw error');
        } catch (error) {
          expect(error.message).to.include('Workflow execution failed');
        }
      });

      expect(walletCheckBalanceStub).to.have.been.calledOnce;
      expect(walletTransferStub).to.have.been.calledOnce;
      expect(paymentMethodTransferStub).to.have.been.calledOnce;
      expect(promoReturnSponsoredFundsStub).to.have.been.calledOnceWith(args);
      expect(promoFailedStub).to.have.been.calledOnceWith(args);
    });

    it('should proceed to success step when payment succeeds', async () => {
      // ARRANGE
      const args = promoWorkflowArgsFactory.build({
        promoAmountCents: 1000,
        purchaseAmountCents: 5000,
      });

      const walletCheckBalanceStub = sandbox.stub().resolves();
      const walletTransferStub = sandbox.stub().resolves();
      const paymentMethodTransferStub = sandbox.stub().resolves({
        journalId: undefined,
        returned: {},
      });
      const promoSucceededStub = sandbox.stub().resolves();

      const { handle, worker } = await startWorker(
        {
          walletCheckBalance: walletCheckBalanceStub,
          walletTransfer: walletTransferStub,
          paymentMethodTransfer: paymentMethodTransferStub,
          promoReturnSponsoredFunds: sandbox.stub().resolves(),
          promoSucceeded: promoSucceededStub,
          promoFailed: sandbox.stub().resolves(),
        },
        args
      );

      // ACT
      await worker.runUntil(async () => handle.result());

      // ASSERT
      expect(walletCheckBalanceStub).to.have.been.calledOnce;
      expect(walletTransferStub).to.have.been.calledOnce;
      expect(paymentMethodTransferStub).to.have.been.calledOnce;
      expect(promoSucceededStub).to.have.been.calledOnceWith(args);
    });
  });

  describe('Error handling scenarios', () => {
    it('should not return promo funds when wallet transfer fails (funds not transferred)', async () => {
      // ARRANGE
      const args = promoWorkflowArgsFactory.build({
        promoAmountCents: 1000,
      });

      const walletCheckBalanceStub = sandbox.stub().resolves();
      const walletTransferStub = sandbox
        .stub()
        .rejects(
          ApplicationFailure.nonRetryable(
            'Wallet transfer failed',
            'WalletTransferFailed'
          )
        );
      const promoReturnSponsoredFundsStub = sandbox.stub().resolves();
      const promoFailedStub = sandbox.stub().resolves();

      const { handle, worker } = await startWorker(
        {
          walletCheckBalance: walletCheckBalanceStub,
          walletTransfer: walletTransferStub,
          paymentMethodTransfer: sandbox.stub().resolves({
            journalId: undefined,
            returned: {},
          }),
          promoReturnSponsoredFunds: promoReturnSponsoredFundsStub,
          promoSucceeded: sandbox.stub().resolves(),
          promoFailed: promoFailedStub,
        },
        args
      );

      // ACT & ASSERT
      await worker.runUntil(async () => {
        try {
          await handle.result();
          expect.fail('Expected workflow to throw error');
        } catch (error) {
          expect(error.message).to.include('Workflow execution failed');
        }
      });

      expect(walletCheckBalanceStub).to.have.been.calledOnce;
      expect(walletTransferStub).to.have.been.calledOnce;
      expect(promoReturnSponsoredFundsStub).to.not.have.been.called; // No funds to return since transfer failed
      expect(promoFailedStub).to.have.been.calledOnceWith(args);
    });

    it('should return promo funds when payment fails after successful transfer', async () => {
      // ARRANGE
      const args = promoWorkflowArgsFactory.build({
        promoAmountCents: 1000,
      });

      const walletCheckBalanceStub = sandbox.stub().resolves();
      const walletTransferStub = sandbox.stub().resolves(); // Transfer succeeds
      const paymentMethodTransferStub = sandbox
        .stub()
        .rejects(
          ApplicationFailure.nonRetryable('Payment failed', 'PaymentFailed')
        );
      const promoReturnSponsoredFundsStub = sandbox.stub().resolves();
      const promoFailedStub = sandbox.stub().resolves();

      const { handle, worker } = await startWorker(
        {
          walletCheckBalance: walletCheckBalanceStub,
          walletTransfer: walletTransferStub,
          paymentMethodTransfer: paymentMethodTransferStub,
          promoReturnSponsoredFunds: promoReturnSponsoredFundsStub,
          promoSucceeded: sandbox.stub().resolves(),
          promoFailed: promoFailedStub,
        },
        args
      );

      // ACT & ASSERT
      await worker.runUntil(async () => {
        try {
          await handle.result();
          expect.fail('Expected workflow to throw error');
        } catch (error) {
          expect(error.message).to.include('Workflow execution failed');
        }
      });

      expect(walletCheckBalanceStub).to.have.been.calledOnce;
      expect(walletTransferStub).to.have.been.calledOnce;
      expect(paymentMethodTransferStub).to.have.been.calledOnce;
      expect(promoReturnSponsoredFundsStub).to.have.been.calledOnceWith(args); // Funds returned since transfer succeeded
      expect(promoFailedStub).to.have.been.calledOnceWith(args);
    });
  });

  describe('Full successful workflow', () => {
    it('should complete all steps successfully', async () => {
      // ARRANGE
      const args = promoWorkflowArgsFactory.build({
        promoAmountCents: 1000,
        purchaseAmountCents: 5000,
      });

      const walletCheckBalanceStub = sandbox.stub().resolves();
      const walletTransferStub = sandbox.stub().resolves();
      const paymentMethodTransferStub = sandbox.stub().resolves({
        journalId: undefined,
        returned: {},
      });
      const promoSucceededStub = sandbox.stub().resolves();

      const { handle, worker } = await startWorker(
        {
          walletCheckBalance: walletCheckBalanceStub,
          walletTransfer: walletTransferStub,
          paymentMethodTransfer: paymentMethodTransferStub,
          promoReturnSponsoredFunds: sandbox.stub().resolves(),
          promoSucceeded: promoSucceededStub,
          promoFailed: sandbox.stub().resolves(),
        },
        args
      );

      // ACT
      await worker.runUntil(async () => handle.result());

      // ASSERT
      expect(walletCheckBalanceStub).to.have.been.calledOnceWith({
        walletId: args.fundingWalletId,
        requiredAmount: args.promoAmountCents,
      });
      expect(walletTransferStub).to.have.been.calledOnce;
      expect(paymentMethodTransferStub).to.have.been.calledOnce;
      expect(promoSucceededStub).to.have.been.calledOnceWith(args);
    });
  });
});
