import { expect } from 'chai';
import {
  getPromoWalletFundingArgs,
  getPromoWalletReversalArgs,
  getPromoTakePaymentArgs,
} from '../../../../src/ng/workflow/factories/promotional_payment.factory';
import { PromotionalPaymentWorkflowArgs } from '../../../../src/ng/workflow/types/promotional_payment.types';

describe('Promotional Payment Factory', () => {
  let mockWorkflowArgs: PromotionalPaymentWorkflowArgs;

  beforeEach(() => {
    mockWorkflowArgs = {
      promoAmountCents: 5000,
      purchaseAmountCents: 10000,
      senderId: 'sender-123',
      senderPaymentMethodId: 'pm-123',
      recipientId: 'recipient-456',
      orderIds: ['order-789'],
      invoiceIds: ['invoice-101'],
      description: 'Test promotional payment',
      purchaseTransactionId: 'purchase-txn-123',
      promoTransactionId: 'promo-txn-456',
      fundingWalletId: 'funding-wallet-123',
    };
  });

  describe('getPromoWalletFundingArgs', () => {
    it('should create WalletTransferParams with capture transaction type', () => {
      // ACT
      const actualResult = getPromoWalletFundingArgs(mockWorkflowArgs);

      // ASSERT
      expect(actualResult.transactionType).to.equal('capture');
      expect(actualResult.description).to.equal('Sponsored promo payment');
      expect(actualResult.amountCents).to.equal(5000);
      expect(actualResult.senderId).to.equal('funding-wallet-123');
      expect(actualResult.recipientId).to.equal('recipient-456');
      expect(actualResult.transactionId).to.equal('promo-txn-456');
      expect(actualResult.relatedTransactions).to.deep.equal([
        'purchase-txn-123',
      ]);
    });

    it('should use the correct text fields for wallet transfer', () => {
      // ACT
      const actualResult = getPromoWalletFundingArgs(mockWorkflowArgs);

      // ASSERT
      expect(actualResult.senderText).to.equal('Sponsored promo payment');
      expect(actualResult.recipientText).to.equal('Sponsored promo payment');
    });

    it('should preserve order and invoice IDs', () => {
      // ACT
      const actualResult = getPromoWalletFundingArgs(mockWorkflowArgs);

      // ASSERT
      expect(actualResult.orderIds).to.deep.equal(['order-789']);
      expect(actualResult.invoiceIds).to.deep.equal(['invoice-101']);
    });
  });

  describe('getPromoWalletReversalArgs', () => {
    it('should create WalletTransferParams with capture transaction type', () => {
      // ACT
      const actualResult = getPromoWalletReversalArgs(mockWorkflowArgs);

      // ASSERT
      expect(actualResult.transactionType).to.equal('capture');
      expect(actualResult.description).to.equal('Sponsored promo payment');
      expect(actualResult.amountCents).to.equal(5000);
      expect(actualResult.senderId).to.equal('recipient-456');
      expect(actualResult.recipientId).to.equal('funding-wallet-123');
      expect(actualResult.transactionId).to.equal('purchase-txn-123');
      expect(actualResult.relatedTransactions).to.deep.equal(['promo-txn-456']);
    });

    it('should reverse the sender and recipient from funding operation', () => {
      // ARRANGE
      const fundingResult = getPromoWalletFundingArgs(mockWorkflowArgs);

      // ACT
      const reversalResult = getPromoWalletReversalArgs(mockWorkflowArgs);

      // ASSERT
      expect(reversalResult.senderId).to.equal(fundingResult.recipientId);
      expect(reversalResult.recipientId).to.equal(fundingResult.senderId);
      expect(reversalResult.transactionType).to.equal('capture');
    });

    it('should use the correct text fields for wallet reversal', () => {
      // ACT
      const actualResult = getPromoWalletReversalArgs(mockWorkflowArgs);

      // ASSERT
      expect(actualResult.senderText).to.equal('Sponsored promo payment');
      expect(actualResult.recipientText).to.equal('Sponsored promo payment');
    });

    it('should preserve order and invoice IDs', () => {
      // ACT
      const actualResult = getPromoWalletReversalArgs(mockWorkflowArgs);

      // ASSERT
      expect(actualResult.orderIds).to.deep.equal(['order-789']);
      expect(actualResult.invoiceIds).to.deep.equal(['invoice-101']);
    });
  });

  describe('getPromoTakePaymentArgs', () => {
    it('should create PaymentMethodTransferParams for the remaining amount', () => {
      // ACT
      const actualResult = getPromoTakePaymentArgs(mockWorkflowArgs);

      // ASSERT
      expect(actualResult.amountCents).to.equal(5000); // 10000 - 5000
      expect(actualResult.buyerId).to.equal('sender-123');
      expect(actualResult.sellerId).to.equal('recipient-456');
      expect(actualResult.paymentMethodId).to.equal('pm-123');
      expect(actualResult.transactionId).to.equal('purchase-txn-123');
      expect(actualResult.relatedTransactions).to.deep.equal(['promo-txn-456']);
    });

    it('should use the provided description for all text fields', () => {
      // ACT
      const actualResult = getPromoTakePaymentArgs(mockWorkflowArgs);

      // ASSERT
      expect(actualResult.description).to.equal('Test promotional payment');
      expect(actualResult.buyerText).to.equal('Test promotional payment');
      expect(actualResult.sellerText).to.equal('Test promotional payment');
    });

    it('should preserve order and invoice IDs', () => {
      // ACT
      const actualResult = getPromoTakePaymentArgs(mockWorkflowArgs);

      // ASSERT
      expect(actualResult.orderIds).to.deep.equal(['order-789']);
      expect(actualResult.invoiceIds).to.deep.equal(['invoice-101']);
    });
  });

  describe('Transaction type consistency', () => {
    it('should ensure all wallet transfer operations use capture transaction type', () => {
      // ACT
      const fundingArgs = getPromoWalletFundingArgs(mockWorkflowArgs);
      const reversalArgs = getPromoWalletReversalArgs(mockWorkflowArgs);

      // ASSERT
      expect(fundingArgs.transactionType).to.equal('capture');
      expect(reversalArgs.transactionType).to.equal('capture');
    });
  });
});
