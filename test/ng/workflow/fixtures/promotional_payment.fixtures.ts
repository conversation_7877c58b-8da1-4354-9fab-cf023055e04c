import { Sync } from 'factory.ts';
import { PromotionalPaymentWorkflowArgs } from '../../../../src/ng/workflow/types/promotional_payment.types';

export const promoWorkflowArgsFactory =
  Sync.makeFactory<PromotionalPaymentWorkflowArgs>({
    promoAmountCents: 0,
    purchaseAmountCents: 0,
    senderId: 'sender-123',
    senderPaymentMethodId: 'pm-123',
    recipientId: 'recipient-456',
    orderId: 'order-789',
    invoiceId: 'invoice-101',
    description: 'Test promo payment',
    purchaseTransactionId: 'purchase-txn-123',
    promoTransactionId: 'promo-txn-456',
    fundingWalletId: 'funding-wallet-123',
  });
