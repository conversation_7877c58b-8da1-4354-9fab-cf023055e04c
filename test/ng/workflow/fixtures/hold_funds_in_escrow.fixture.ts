import { v4 as uuidv4 } from 'uuid';
import { Sync } from 'factory.ts';
import { HoldFundsInEscrow } from '../../../../src/ng/workflow/activities/wallet.activities';
import { CurrencyCodes } from '../../../../src/types/currency_codes';

export const holdFundsInEscrowFactory = Sync.makeFactory<HoldFundsInEscrow>({
  description: 'Test upfront payment',
  amountCents: 1000,
  sellerId: 'seller123',
  sellerText: 'Seller',
  buyerId: 'buyer123',
  buyerText: 'Buyer',
  currencyCode: CurrencyCodes.AUD,
  paymentMethodId: 'pm123',
  orderIds: ['order123'],
  invoiceIds: ['invoice123'],
  transactionId: uuidv4(),
});
