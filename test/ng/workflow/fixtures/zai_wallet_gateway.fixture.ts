import { stubInterface, ObjectMethodsMap } from 'ts-sinon';
import { ZaiWalletsGateway } from '../../../../src/ng/domain/services/gateways/zai_wallets.gateway';

/**
 *
 */
export const zaiWalletsGatewayFixture = (
  overrides?: ObjectMethodsMap<ZaiWalletsGateway>
) => {
  const stub = stubInterface<ZaiWalletsGateway>({
    getUserWallet: Promise.resolve({
      accountId: 'user123',
      balance: 10000,
    }),
    ...(overrides ?? {}),
  });

  return stub;
};
