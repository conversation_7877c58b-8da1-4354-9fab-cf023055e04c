import { Sync } from 'factory.ts';
import { ObjectMethodsMap, stubInterface } from 'ts-sinon';
import { v4 as uuid } from 'uuid';
import {
  Backend,
  states,
  TransactionModel,
  TransactionInstance,
} from '../../../../src/models/transaction';
import { TransactionsRepository } from '../../../../src/ng/domain/repositories/transactions.repository';
import { TransactionService } from '../../../../src/ng/domain/services/domain/transaction.service';
import { CurrencyCodes } from '../../../../src/types/currency_codes';

export const transactionFactory = Sync.makeFactory<TransactionModel>({
  id: uuid(),
  name: 'Test Transaction',
  state: states.pending,
  backend: Backend.PROMISEPAY,
  paymentMethodId: null,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  description: 'Test transaction',
  seller_id: uuid(),
  buyer_id: uuid(),
  amount: 1000,
  log: [],
  currency: CurrencyCodes.AUD,
  release_at: undefined,
  received_at: undefined,
  declineReason: undefined,
  declineCode: undefined,
  webhook: undefined,
  reversal_webhook: undefined,
  settlement_delay_hours: 0,
  fee: 0,
  settlementAmount: 1000,
  type: 'unknown',
  lockedAt: undefined,
  sentAt: undefined,
  reference: '',
  settledAt: undefined,
  wallet_payment_method_id: null,
  settlement_id: null,
  is_reversal: false,
  batchId: null,
  userId: null,
  bank_payment_method_id: null,
  card_payment_method_id: null,
  refundedById: null,
  relatedTransactions: [],
  invoiceIds: [],
  refunded_at: null,
  refund_amount: 0,
  refund_data: null,
  fundedAt: undefined,
  orderIds: [],
  transactionType: 'capture',
  externalChargeId: undefined,
  context: undefined,
});

/**
 *
 * @returns
 */
export const transactionsRepositoryFactory = (props?: {
  transactions?: Partial<TransactionInstance>[];
  overrides?: ObjectMethodsMap<TransactionsRepository>;
}) => {
  const transactionsRepository = stubInterface<TransactionsRepository>({
    ...(props?.overrides ?? {}),
  });

  props?.transactions?.forEach((transaction, index) => {
    transactionsRepository.getTransactionById
      .onCall(index)
      .resolves(transactionFactory.build(transaction) as TransactionInstance);
  });

  transactionsRepository.getTransactionsByIds.resolves(
    (props?.transactions ?? []) as TransactionInstance[]
  );

  return transactionsRepository;
};

/**
 * Create a transaction service fixture
 * @param props
 * @returns
 */
export const transactionServiceFactory = (props?: {
  overrides?: ObjectMethodsMap<TransactionService>;
}) =>
  stubInterface<TransactionService>({
    getOrCreateTransaction: Promise.resolve(
      transactionFactory.build() as TransactionInstance
    ),
    ...(props?.overrides ?? {}),
  });
