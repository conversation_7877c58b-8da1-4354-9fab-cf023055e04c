import { Sync } from 'factory.ts';
import { UpfrontPayment } from '../../../../src/ng/common/requests/upfront_payments.request';

export const upfrontPaymentFactory = Sync.makeFactory<UpfrontPayment>({
  amountCents: 1000,
  description: 'Test upfront payment',
  sellerUserId: 'seller123',
  buyerUserId: 'buyer123',
  paymentMethodId: 'pm123',
  orderId: 'order123',
  userId: 'user123',
  invoiceId: 'invoice123',
});
