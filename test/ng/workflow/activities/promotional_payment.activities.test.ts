import sinon, { SinonSandbox } from 'sinon';
import { StubbedInstance, stubInterface } from 'ts-sinon';
import { expect } from 'chai';
import { Transaction } from 'sequelize';
import { createPromoActivities } from '../../../../src/ng/workflow/activities/promotional_payment.activities';
import { TransactionService } from '../../../../src/ng/domain/services/domain/transaction.service';
import { sequelize } from '../../../../src/models';
import { omClient } from '../../../../src/lib/om_client';
import * as stats from '../../../../src/lib/stats';
import { promoWorkflowArgsFactory } from '../fixtures/promotional_payment.fixtures';
import { WorkflowFactory } from '../../../../src/ng/workflow/workflow.factory';

describe('Promotional payment activities', () => {
  let sandbox: SinonSandbox;
  let transactionService: StubbedInstance<TransactionService>;
  let workflowFactory: StubbedInstance<WorkflowFactory>;
  let activities: ReturnType<typeof createPromoActivities>;
  let sequelizeTransactionStub: sinon.SinonStub;
  let omClientPostStub: sinon.SinonStub;
  let incrementStub: sinon.SinonStub;

  beforeEach(() => {
    sandbox = sinon.createSandbox();

    // Create dependency stubs
    transactionService = stubInterface<TransactionService>();
    workflowFactory = stubInterface<WorkflowFactory>();

    // Mock global dependencies
    sequelizeTransactionStub = sandbox
      .stub(sequelize, 'transaction')
      .callsFake(((callback: (txn: Transaction) => Promise<unknown>) => {
        const mockTxn = {} as Transaction;
        return callback(mockTxn);
      }) as typeof sequelize.transaction);

    omClientPostStub = sandbox.stub(omClient, 'post').resolves();
    incrementStub = sandbox.stub(stats, 'increment');

    // Create activities instance
    activities = createPromoActivities(transactionService, workflowFactory);
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('promoReturnSponsoredFunds', () => {
    it('should call workflow factory to start child wallet transfer', async () => {
      // ARRANGE
      const args = promoWorkflowArgsFactory.build({
        promoAmountCents: 1000,
        purchaseAmountCents: 5000,
        fundingWalletId: 'funding-wallet-123',
      });

      workflowFactory.startChildWalletTransfer.resolves();

      // ACT
      await activities.promoReturnSponsoredFunds(args);

      // ASSERT
      expect(workflowFactory.startChildWalletTransfer.calledOnce).to.be.true;

      // Verify the call was made with reversal args
      const callArgs =
        workflowFactory.startChildWalletTransfer.firstCall.args[0];
      expect(callArgs).to.be.an('object');
    });

    it('should propagate errors from workflow factory', async () => {
      // ARRANGE
      const args = promoWorkflowArgsFactory.build({
        promoAmountCents: 1000,
        purchaseAmountCents: 5000,
      });

      const error = new Error('Wallet transfer failed');
      workflowFactory.startChildWalletTransfer.rejects(error);

      // ACT & ASSERT
      try {
        await activities.promoReturnSponsoredFunds(args);
        expect.fail('Expected error to be thrown');
      } catch (err) {
        expect(err).to.equal(error);
        expect(workflowFactory.startChildWalletTransfer.calledOnce).to.be.true;
      }
    });
  });

  describe('promoSucceeded', () => {
    it('should mark transactions as released and completed, then notify OM', async () => {
      // ARRANGE
      const args = promoWorkflowArgsFactory.build({
        promoAmountCents: 1000,
        purchaseAmountCents: 5000,
        purchaseTransactionId: 'purchase-txn-123',
        promoTransactionId: 'promo-txn-456',
        orderIds: ['order-789'],
        invoiceIds: ['invoice-101'],
        recipientId: 'recipient-123',
      });

      transactionService.transactionsReleaseNow.resolves();
      transactionService.markCompleted.resolves();

      // ACT
      await activities.promoSucceeded(args);

      // ASSERT
      // Verify sequelize transaction was used
      expect(sequelizeTransactionStub.calledOnce).to.be.true;

      // Verify transactionsReleaseNow was called twice (purchase and promo)
      expect(transactionService.transactionsReleaseNow.calledTwice).to.be.true;

      // Verify purchase transaction release
      const releaseCall1 =
        transactionService.transactionsReleaseNow.firstCall.args[0];
      expect(releaseCall1.transactionId).to.equal(args.purchaseTransactionId);
      expect(releaseCall1.sellerId).to.equal(args.recipientId);
      expect(releaseCall1.settlementAmountCents).to.equal(
        args.purchaseAmountCents
      );
      expect(releaseCall1.txn).to.be.an('object');

      // Verify promo transaction release
      const releaseCall2 =
        transactionService.transactionsReleaseNow.secondCall.args[0];
      expect(releaseCall2.transactionId).to.equal(args.promoTransactionId);
      expect(releaseCall2.sellerId).to.equal(args.recipientId);
      expect(releaseCall2.settlementAmountCents).to.equal(
        args.promoAmountCents
      );
      expect(releaseCall2.txn).to.be.an('object');

      // Verify markCompleted was called twice
      expect(transactionService.markCompleted.calledTwice).to.be.true;

      // Verify purchase transaction was marked as completed
      const completedCall1 = transactionService.markCompleted.firstCall.args[0];
      expect(completedCall1.transactionId).to.equal(args.purchaseTransactionId);
      expect(completedCall1.emitChangeEvent).to.be.false;
      expect(completedCall1.receivedAt).to.be.a('string');
      expect(completedCall1.txn).to.be.an('object');

      // Verify promo transaction was marked as completed
      const completedCall2 =
        transactionService.markCompleted.secondCall.args[0];
      expect(completedCall2.transactionId).to.equal(args.promoTransactionId);
      expect(completedCall2.emitChangeEvent).to.be.false;
      expect(completedCall2.receivedAt).to.be.a('string');
      expect(completedCall2.txn).to.be.an('object');

      // Verify OM client was called to update order status
      expect(omClientPostStub.calledOnce).to.be.true;
      expect(
        omClientPostStub.calledWith('/v1/transactions/complete', {
          status: 'success',
          orderIds: args.orderIds,
          invoiceIds: args.invoiceIds,
          transactions: [
            {
              id: args.purchaseTransactionId,
              type: 'purchase',
            },
            {
              id: args.promoTransactionId,
              type: 'promotional',
            },
          ],
        })
      ).to.be.true;

      // Verify success metric was incremented
      expect(incrementStub.calledOnce).to.be.true;
      expect(incrementStub.calledWith('promo_succeeded')).to.be.true;
    });

    it('should handle errors and throw workflow error', async () => {
      // ARRANGE
      const args = promoWorkflowArgsFactory.build({
        promoAmountCents: 1000,
        purchaseAmountCents: 5000,
        purchaseTransactionId: 'purchase-txn-123',
        promoTransactionId: 'promo-txn-456',
        orderIds: ['order-789'],
        invoiceIds: ['invoice-101'],
      });
      const error = new Error('Database error');

      transactionService.transactionsReleaseNow.rejects(error);

      // ACT & ASSERT
      try {
        await activities.promoSucceeded(args);
        expect.fail('Expected error to be thrown');
      } catch (err) {
        // Verify error metric was incremented
        expect(incrementStub.calledWith('promo_succeeded_error')).to.be.true;

        // Verify workflow error was created correctly
        expect(err.message).to.contain('Failed to finalise promo payment');
      }
    });
  });

  describe('promoFailed', () => {
    it('should mark transactions as failed and notify OM', async () => {
      // ARRANGE
      const args = promoWorkflowArgsFactory.build({
        promoAmountCents: 1000,
        purchaseAmountCents: 5000,
        purchaseTransactionId: 'purchase-txn-123',
        promoTransactionId: 'promo-txn-456',
        orderIds: ['order-789'],
        invoiceIds: ['invoice-101'],
      });

      transactionService.markFailed.resolves();

      // ACT
      await activities.promoFailed(args);

      // ASSERT
      // Verify sequelize transaction was used
      expect(sequelizeTransactionStub.calledOnce).to.be.true;

      // Verify markFailed was called twice
      expect(transactionService.markFailed.calledTwice).to.be.true;

      // Verify purchase transaction was marked as failed
      const failedCall1 = transactionService.markFailed.firstCall.args[0];
      expect(failedCall1.transactionId).to.equal(args.purchaseTransactionId);
      expect(failedCall1.declineReason).to.equal('Promo failed');
      expect(failedCall1.txn).to.be.an('object');

      // Verify promo transaction was marked as failed
      const failedCall2 = transactionService.markFailed.secondCall.args[0];
      expect(failedCall2.transactionId).to.equal(args.promoTransactionId);
      expect(failedCall2.declineReason).to.equal('Promo failed');
      expect(failedCall2.txn).to.be.an('object');

      // Verify OM client was called to update order status
      expect(omClientPostStub.calledOnce).to.be.true;
      expect(
        omClientPostStub.calledWith('/v1/transactions/complete', {
          status: 'failed',
          orderIds: args.orderIds,
          invoiceIds: args.invoiceIds,
          transactions: [
            {
              id: args.purchaseTransactionId,
              type: 'purchase',
            },
            {
              id: args.promoTransactionId,
              type: 'promotional',
            },
          ],
        })
      ).to.be.true;

      // Verify failure metric was incremented
      expect(incrementStub.calledWith('promo_failed')).to.be.true;
    });

    it('should handle errors and rethrow them', async () => {
      // ARRANGE
      const args = promoWorkflowArgsFactory.build({
        promoAmountCents: 1000,
        purchaseAmountCents: 5000,
        purchaseTransactionId: 'purchase-txn-123',
        promoTransactionId: 'promo-txn-456',
        orderIds: ['order-789'],
        invoiceIds: ['invoice-101'],
      });
      const error = new Error('Database error');

      transactionService.markFailed.rejects(error);

      // ACT & ASSERT
      try {
        await activities.promoFailed(args);
        expect.fail('Expected error to be thrown');
      } catch (err) {
        // Verify error metric was incremented
        expect(incrementStub.calledWith('promo_failed_error')).to.be.true;

        // Verify original error was rethrown
        expect(err).to.equal(error);
      }
    });
  });
});
