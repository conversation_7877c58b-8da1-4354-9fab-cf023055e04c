import { v4 as uuid } from 'uuid';
import { StubbedInstance, stubInterface } from 'ts-sinon';
import { expect } from 'chai';
import sinon from 'sinon';
import { ApplicationFailure } from '@temporalio/common';
import { UserService } from '../../../../src/ng/domain/services/domain/user.service';
import { createUpfrontControlActivities } from '../../../../src/ng/workflow/activities/upfront_control.activities';
import { userServiceFactory, userFactory } from '../fixtures/user.fixture';
import { UpfrontPayment } from '../../../../src/ng/common/requests/upfront_payments.request';
import { upfrontPaymentFactory } from '../fixtures/upfront.fixture';
import { TransactionService } from '../../../../src/ng/domain/services/domain/transaction.service';
import { UpfrontPaymentState } from '../../../../src/ng/workflow/types/upfront.types';
import { omClient } from '../../../../src/lib/om_client';
import * as stats from '../../../../src/lib/stats';
import { Backend } from '../../../../src/models/transaction';
import { UpfrontFlowStep } from '../../../../src/types/upfront.types';

describe('Upfront Control Activities', () => {
  let activities: ReturnType<typeof createUpfrontControlActivities>;
  let userService: StubbedInstance<UserService>;
  let transactionService: StubbedInstance<TransactionService>;
  let request: UpfrontPayment;
  let sandbox: sinon.SinonSandbox;
  let omClientPatchStub: sinon.SinonStub;
  let incrementStub: sinon.SinonStub;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    userService = userServiceFactory();
    transactionService = stubInterface<TransactionService>();

    activities = createUpfrontControlActivities(
      userService,
      transactionService
    );

    request = upfrontPaymentFactory.build();

    omClientPatchStub = sandbox.stub(omClient, 'patch').resolves();
    incrementStub = sandbox.stub(stats, 'increment');
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('start', () => {
    it('should start the upfront payment workflow with valid data', async () => {
      // ARRANGE
      const buyer = userFactory.build({ backend: Backend.PROMISEPAY });
      const seller = userFactory.build({ backend: Backend.PROMISEPAY });

      userService.safeGetUser
        .onFirstCall()
        .resolves(buyer)
        .onSecondCall()
        .resolves(seller);

      // ACT
      await activities.upfrontStart(request);

      // ASSERT
      expect(userService.safeGetUser.calledTwice).to.be.true;
      expect(userService.safeGetUser.firstCall.args[0]).to.equal(
        request.buyerUserId
      );
      expect(userService.safeGetUser.secondCall.args[0]).to.equal(
        request.sellerUserId
      );
    });

    it('should throw an error if the buyer is not found', async () => {
      // ARRANGE
      userService.safeGetUser.onFirstCall().resolves(undefined);

      // ACT & ASSERT
      try {
        await activities.upfrontStart(request);
        expect.fail('Expected an error to be thrown');
      } catch (err) {
        expect(err).to.be.instanceOf(ApplicationFailure);
        expect(err.message).to.equal('Buyer not found');
        expect(err.nonRetryable).to.be.false;
      }
    });

    it('should throw an error if the seller is not found', async () => {
      // ARRANGE
      const buyer = userFactory.build({ backend: Backend.PROMISEPAY });
      userService.safeGetUser
        .onFirstCall()
        .resolves(buyer)
        .onSecondCall()
        .resolves(undefined);

      // ACT & ASSERT
      try {
        await activities.upfrontStart(request);
        expect.fail('Expected an error to be thrown');
      } catch (err) {
        expect(err).to.be.instanceOf(ApplicationFailure);
        expect(err.message).to.equal('Seller not found');
        expect(err.nonRetryable).to.be.true;
      }
    });

    it('should throw an error if buyer backend is not promisepay', async () => {
      // ARRANGE
      const buyer = userFactory.build({ backend: Backend.STRIPE });
      userService.safeGetUser.onFirstCall().resolves(buyer);

      // ACT & ASSERT
      try {
        await activities.upfrontStart(request);
        expect.fail('Expected an error to be thrown');
      } catch (err) {
        expect(err).to.be.instanceOf(ApplicationFailure);
        expect(err.message).to.equal('Buyer backend is not Promisepay');
        expect(err.nonRetryable).to.be.false;
      }
    });

    it('should throw an error if seller backend is not promisepay', async () => {
      // ARRANGE
      const buyer = userFactory.build({ backend: Backend.PROMISEPAY });
      const seller = userFactory.build({ backend: Backend.STRIPE });
      userService.safeGetUser
        .onFirstCall()
        .resolves(buyer)
        .onSecondCall()
        .resolves(seller);

      // ACT & ASSERT
      try {
        await activities.upfrontStart(request);
        expect.fail('Expected an error to be thrown');
      } catch (err) {
        expect(err).to.be.instanceOf(ApplicationFailure);
        expect(err.message).to.equal('Seller backend is not Promisepay');
        expect(err.nonRetryable).to.be.false;
      }
    });
  });

  describe('holdingTransactionSucceeded', () => {
    const holdingTransactionId = uuid();

    beforeEach(() => {
      transactionService.markCompleted.resolves();
    });

    it('should mark transaction as completed and notify OM successfully', async () => {
      // ACT
      await activities.upfrontHoldingTransactionSucceeded({
        holdingTransactionId,
        upfront: request,
      });

      // ASSERT
      expect(transactionService.markCompleted.calledOnce).to.be.true;
      const markCompletedArgs =
        transactionService.markCompleted.firstCall.args[0];
      expect(markCompletedArgs.transactionId).to.equal(holdingTransactionId);
      expect(markCompletedArgs.emitChangeEvent).to.be.false;
      expect(markCompletedArgs.receivedAt).to.be.a('string');

      expect(omClientPatchStub.calledOnce).to.be.true;
      expect(omClientPatchStub.firstCall.args[0]).to.equal(
        `/v1/orders/${request.orderId}/upfront/holding-finished`
      );
      expect(omClientPatchStub.firstCall.args[1]).to.deep.equal({
        orderId: request.orderId,
        status: 'success',
        invoiceId: request.invoiceId,
        holdingTransactionId,
      });

      expect(
        incrementStub.calledOnceWith(
          'upfront_payment_holding_transaction_succeeded'
        )
      ).to.be.true;
    });

    it('should handle transaction service errors', async () => {
      // ARRANGE
      const error = new Error('Transaction service error');
      transactionService.markCompleted.rejects(error);

      // ACT & ASSERT
      try {
        await activities.upfrontHoldingTransactionSucceeded({
          holdingTransactionId,
          upfront: request,
        });
        expect.fail('Expected an error to be thrown');
      } catch (err) {
        expect(
          incrementStub.calledOnceWith(
            'upfront_payment_holding_transaction_failed'
          )
        ).to.be.true;
      }
    });

    it('should handle OM client errors', async () => {
      // ARRANGE
      const error = new Error('OM client error');
      omClientPatchStub.rejects(error);

      // ACT & ASSERT
      try {
        await activities.upfrontHoldingTransactionSucceeded({
          holdingTransactionId,
          upfront: request,
        });
        expect.fail('Expected an error to be thrown');
      } catch (err) {
        expect(
          incrementStub.calledOnceWith(
            'upfront_payment_holding_transaction_failed'
          )
        ).to.be.true;
      }
    });
  });

  describe('holdingTransactionFailed', () => {
    const holdingTransactionId = uuid();
    const error = new Error('Payment failed');

    beforeEach(() => {
      transactionService.markFailed.resolves();
    });

    it('should mark transaction as failed and notify OM successfully', async () => {
      // ACT
      await activities.upfrontHoldingTransactionFailed({
        upfront: request,
        holdingTransactionId,
        error,
      });

      // ASSERT
      expect(transactionService.markFailed.calledOnce).to.be.true;
      expect(transactionService.markFailed.firstCall.args[0]).to.deep.equal({
        transactionId: holdingTransactionId,
        declineReason: error.message,
        emitChangeEvent: false,
      });

      expect(omClientPatchStub.calledOnce).to.be.true;
      expect(omClientPatchStub.firstCall.args[0]).to.equal(
        `/v1/orders/${request.orderId}/upfront/holding-finished`
      );
      expect(omClientPatchStub.firstCall.args[1]).to.deep.equal({
        orderId: request.orderId,
        status: 'failed',
        invoiceId: request.invoiceId,
        holdingTransactionId,
      });

      expect(incrementStub.calledOnceWith('upfront_payment_finalise_succeeded'))
        .to.be.true;
    });

    it('should handle non-Error objects', async () => {
      // ACT
      await activities.upfrontHoldingTransactionFailed({
        upfront: request,
        holdingTransactionId,
        error: new Error('string error'),
      });

      // ASSERT
      expect(
        transactionService.markFailed.firstCall.args[0].declineReason
      ).to.equal('string error');
    });

    it('should handle transaction service errors', async () => {
      // ARRANGE
      const serviceError = new Error('Transaction service error');
      transactionService.markFailed.rejects(serviceError);

      // ACT & ASSERT
      try {
        await activities.upfrontHoldingTransactionFailed({
          upfront: request,
          holdingTransactionId,
          error,
        });
        expect.fail('Expected an error to be thrown');
      } catch (err) {
        expect(incrementStub.calledOnceWith('upfront_payment_finalise_failed'))
          .to.be.true;
      }
    });
  });

  describe('finaliseSucceeded', () => {
    const state: UpfrontPaymentState = {
      holdingTransactionId: uuid(),
      topUpTransactionId: uuid(),
      holdingAmountCents: 1000,
      flowStep: UpfrontFlowStep.WaitingForHoldingPayment,
    };

    beforeEach(() => {
      transactionService.markCompleted.resolves();
    });

    it('should complete topup transaction and notify OM when topUpTransactionId exists', async () => {
      // ACT
      await activities.upfrontFinaliseSucceeded(request, state);

      // ASSERT
      expect(transactionService.markCompleted.calledOnce).to.be.true;
      const markCompletedArgs =
        transactionService.markCompleted.firstCall.args[0];
      expect(markCompletedArgs.transactionId).to.equal(
        state.topUpTransactionId
      );
      expect(markCompletedArgs.emitChangeEvent).to.be.false;
      expect(markCompletedArgs.receivedAt).to.be.a('string');

      expect(omClientPatchStub.calledOnce).to.be.true;
      expect(omClientPatchStub.firstCall.args[0]).to.equal(
        `/v1/orders/${request.orderId}/upfront/finalise-finished`
      );
      expect(omClientPatchStub.firstCall.args[1]).to.deep.equal({
        orderId: request.orderId,
        status: 'success',
        invoiceId: request.invoiceId,
        holdingTransactionId: state.holdingTransactionId,
        topUpTransactionId: state.topUpTransactionId,
      });
    });

    it('should skip transaction completion when topUpTransactionId is undefined', async () => {
      // ARRANGE
      const stateWithoutTopUp = {
        ...state,
        topUpTransactionId: '',
      };

      // ACT
      await activities.upfrontFinaliseSucceeded(request, stateWithoutTopUp);

      // ASSERT
      expect(transactionService.markCompleted.called).to.be.false;
      expect(omClientPatchStub.calledOnce).to.be.true;
    });

    it('should handle errors and throw workflow error', async () => {
      // ARRANGE
      const error = new Error('Finalise error');
      omClientPatchStub.rejects(error);

      // ACT & ASSERT
      try {
        await activities.upfrontFinaliseSucceeded(request, state);
        expect.fail('Expected an error to be thrown');
      } catch (err) {
        expect(
          incrementStub.calledOnceWith('upfront_payment_finalise_succeeded')
        ).to.be.true;
      }
    });
  });

  describe('finaliseFailed', () => {
    const state: UpfrontPaymentState = {
      holdingTransactionId: uuid(),
      topUpTransactionId: uuid(),
      holdingAmountCents: 1000,
      flowStep: UpfrontFlowStep.WaitingForHoldingPayment,
    };
    const error = new Error('Finalise failed');

    beforeEach(() => {
      transactionService.markFailed.resolves();
    });

    it('should mark topup transaction as failed and notify OM', async () => {
      // ACT
      await activities.upfrontFinaliseFailed(request, state, error);

      // ASSERT
      expect(transactionService.markFailed.calledOnce).to.be.true;
      expect(transactionService.markFailed.firstCall.args[0]).to.deep.equal({
        transactionId: state.topUpTransactionId,
        declineReason: error.message,
        emitChangeEvent: false,
      });

      expect(omClientPatchStub.calledOnce).to.be.true;
      expect(omClientPatchStub.firstCall.args[0]).to.equal(
        `/v1/orders/${request.orderId}/upfront/finalise-finished`
      );
      expect(omClientPatchStub.firstCall.args[1]).to.deep.equal({
        orderId: request.orderId,
        status: 'failed',
        invoiceId: request.invoiceId,
        holdingTransactionId: state.holdingTransactionId,
        topUpTransactionId: state.topUpTransactionId,
      });

      expect(incrementStub.calledOnceWith('upfront_payment_finalise_succeeded'))
        .to.be.true;
    });

    it('should handle non-Error objects as decline reason', async () => {
      // ACT
      await activities.upfrontFinaliseFailed(request, state, 'string error');

      // ASSERT
      expect(
        transactionService.markFailed.firstCall.args[0].declineReason
      ).to.equal('Failed to process payment');
    });

    it('should handle transaction service errors', async () => {
      // ARRANGE
      const serviceError = new Error('Transaction service error');
      transactionService.markFailed.rejects(serviceError);

      // ACT & ASSERT
      try {
        await activities.upfrontFinaliseFailed(request, state, error);
        expect.fail('Expected an error to be thrown');
      } catch (err) {
        expect(incrementStub.calledOnceWith('upfront_payment_finalise_failed'))
          .to.be.true;
      }
    });

    it('should handle OM client errors', async () => {
      // ARRANGE
      const omError = new Error('OM client error');
      omClientPatchStub.rejects(omError);

      // ACT & ASSERT
      try {
        await activities.upfrontFinaliseFailed(request, state, error);
        expect.fail('Expected an error to be thrown');
      } catch (err) {
        expect(incrementStub.calledOnceWith('upfront_payment_finalise_failed'))
          .to.be.true;
      }
    });
  });
});
