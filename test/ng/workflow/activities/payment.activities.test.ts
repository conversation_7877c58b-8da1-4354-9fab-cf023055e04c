import sinon, { createStubInstance, SinonSandbox } from 'sinon';
import { StubbedInstance, stubInterface } from 'ts-sinon';
import { expect } from 'chai';
import { ApplicationFailure } from '@temporalio/common';
import { v4 as uuid } from 'uuid';
import { Item } from 'zai-payments';
import { ConnectionError as SequelizeError } from 'sequelize';
import {
  createPaymentActivities,
  MakePaymentResult,
} from '../../../../src/ng/workflow/activities/payment.activities';
import { TransactionsRepository } from '../../../../src/ng/domain/repositories/transactions.repository';
import { TransactionService } from '../../../../src/ng/domain/services/domain/transaction.service';
import { UserRepository } from '../../../../src/ng/domain/repositories/user.repository';
import { ZaiFundTransferGateway } from '../../../../src/ng/domain/services/gateways/zai_fund_transfer.gateway';
import {
  states,
  TransactionInstance,
} from '../../../../src/models/transaction';
import { userFactory, userRepositoryFactory } from '../fixtures/user.fixture';
import { zaiFundTransferGatewayFixture } from '../fixtures/zai_fund_transfer_gateway.fixture';
import logger from '../../../../src/lib/logger';
import { sequelize } from '../../../../src/models';
import { MakeZaiPaymentWorkflowInput } from '../../../../src/ng/workflow/definitions/make_zai_payment.workflow';

describe('Payment Activities', () => {
  let sandbox: SinonSandbox;
  let transactionsRepository: StubbedInstance<TransactionsRepository>;
  let transactionService: StubbedInstance<TransactionService>;
  let userRepository: StubbedInstance<UserRepository>;
  let zaiFundTransferGateway: StubbedInstance<ZaiFundTransferGateway>;
  let activities: ReturnType<typeof createPaymentActivities>;
  let mockTransaction: TransactionInstance;

  const transactionId = uuid();
  const paymentMethodId = 'pm123';
  const sellerMobileNumber = '+61400000000';

  before(() => {
    sandbox = sinon.createSandbox();
  });

  beforeEach(() => {
    sandbox.restore();

    transactionsRepository = stubInterface<TransactionsRepository>();
    transactionService = createStubInstance(TransactionService);
    userRepository = userRepositoryFactory({
      users: [{ id: 'seller123' }, { id: 'buyer123' }],
    });
    zaiFundTransferGateway = zaiFundTransferGatewayFixture();

    sandbox.stub(sequelize, 'transaction').callsFake(async (callback: any) => {
      const result = await callback();
      return result;
    });

    mockTransaction = stubInterface<TransactionInstance>();
    mockTransaction.id = transactionId;
    mockTransaction.state = states.pending;
    mockTransaction.seller_id = 'seller123';
    mockTransaction.buyer_id = 'buyer123';

    activities = createPaymentActivities(
      transactionsRepository,
      transactionService,
      userRepository,
      zaiFundTransferGateway,
      logger
    );
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('makePaymentOnTransaction', () => {
    beforeEach(() => {
      // Default successful setup
      transactionsRepository.getTransactionById.resolves(mockTransaction);
      zaiFundTransferGateway.getItem.resolves({
        state: states.pending,
      } as Item);
      zaiFundTransferGateway.makePayment.resolves();
    });

    it('should successfully make payment on transaction', async () => {
      const result = await activities.makePaymentOnTransaction(
        transactionId,
        paymentMethodId,
        sellerMobileNumber
      );

      expect(result).to.deep.equal({
        success: true,
        reason: 'Payment successful',
        action: 'payment_success',
      });

      expect(transactionsRepository.getTransactionById.calledOnce).to.be.true;
      expect(zaiFundTransferGateway.getItem.calledOnce).to.be.true;
      expect(zaiFundTransferGateway.makePayment.calledOnce).to.be.true;
      expect(zaiFundTransferGateway.makePayment.firstCall.args).to.deep.equal([
        transactionId,
        paymentMethodId,
        sellerMobileNumber,
      ]);
    });

    it('should return no_action_needed when transaction not found', async () => {
      transactionsRepository.getTransactionById.resolves(null);

      const result = await activities.makePaymentOnTransaction(
        transactionId,
        paymentMethodId,
        sellerMobileNumber
      );

      expect(result).to.deep.equal({
        success: false,
        reason: 'Transaction not found',
        action: 'no_action_needed',
      });
    });

    it('should return no_action_needed when transaction is not in payable state', async () => {
      const completedTransaction = stubInterface<TransactionInstance>();
      completedTransaction.id = transactionId;
      completedTransaction.state = states.completed;
      transactionsRepository.getTransactionById.resolves(completedTransaction);

      const result = await activities.makePaymentOnTransaction(
        transactionId,
        paymentMethodId,
        sellerMobileNumber
      );

      expect(result).to.deep.equal({
        success: false,
        reason: `Transaction in non-payable state, current state: ${states.completed}`,
        action: 'no_action_needed',
      });
    });

    it('should handle retryable error when getting gateway item', async () => {
      const retryableError = new Error('Network timeout');
      (retryableError as any).status = 503;
      zaiFundTransferGateway.getItem.rejects(retryableError);

      try {
        await activities.makePaymentOnTransaction(
          transactionId,
          paymentMethodId,
          sellerMobileNumber
        );
        expect.fail('Expected error to be thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(ApplicationFailure);
        expect((error as ApplicationFailure).nonRetryable).to.be.false;
      }
    });

    it('should return sync_state with failed when getting gateway item fails with non-retryable error', async () => {
      const nonRetryableError = new Error('Bad request');
      (nonRetryableError as any).status = 400;
      zaiFundTransferGateway.getItem.rejects(nonRetryableError);

      const result = await activities.makePaymentOnTransaction(
        transactionId,
        paymentMethodId,
        sellerMobileNumber
      );

      expect(result).to.deep.equal({
        success: false,
        reason: 'Failed to get gateway item',
        action: 'sync_state',
        state: states.failed,
      });
    });

    it('should return sync_state with failed when gateway item not found', async () => {
      zaiFundTransferGateway.getItem.resolves({} as Item);

      const result = await activities.makePaymentOnTransaction(
        transactionId,
        paymentMethodId,
        sellerMobileNumber
      );

      expect(result).to.deep.equal({
        success: false,
        reason: 'Gateway item not found for transaction',
        action: 'sync_state',
        state: states.failed,
      });
    });

    it('should return sync_state when transaction is already finalized in gateway', async () => {
      zaiFundTransferGateway.getItem.resolves({
        state: states.completed,
      } as Item);

      const result = await activities.makePaymentOnTransaction(
        transactionId,
        paymentMethodId,
        sellerMobileNumber
      );

      expect(result).to.deep.equal({
        success: true,
        reason: 'Transaction finalized in gateway',
        action: 'sync_state',
        state: states.completed,
      });
      expect(zaiFundTransferGateway.makePayment.called).to.be.false;
    });

    it('should handle retryable error during payment', async () => {
      const retryableError = new Error('Service unavailable');
      (retryableError as any).status = 503;
      zaiFundTransferGateway.makePayment.rejects(retryableError);

      try {
        await activities.makePaymentOnTransaction(
          transactionId,
          paymentMethodId,
          sellerMobileNumber
        );
        expect.fail('Expected error to be thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(ApplicationFailure);
        expect((error as ApplicationFailure).nonRetryable).to.be.false;
      }
    });

    it('should return payment_failed when payment fails with non-retryable error', async () => {
      const paymentError = new Error('Insufficient funds');
      (paymentError as any).status = 400;
      zaiFundTransferGateway.makePayment.rejects(paymentError);

      const result = await activities.makePaymentOnTransaction(
        transactionId,
        paymentMethodId,
        sellerMobileNumber
      );

      expect(result.success).to.be.false;
      expect(result.action).to.equal('payment_failed');
    });

    it('should handle database transaction errors', async () => {
      const dbError = new SequelizeError(
        new Error('Database connection failed')
      );
      transactionsRepository.getTransactionById.rejects(dbError);

      try {
        await activities.makePaymentOnTransaction(
          transactionId,
          paymentMethodId,
          sellerMobileNumber
        );
        expect.fail('Expected error to be thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(ApplicationFailure);
        expect((error as ApplicationFailure).nonRetryable).to.be.false;
      }
    });
  });

  describe('finalizeTransaction', () => {
    const mockSeller = userFactory.build({ id: 'seller123' });

    beforeEach(() => {
      transactionsRepository.getTransactionById.resolves(mockTransaction);
      userRepository.getUserById.resolves(mockSeller);
    });

    it('should skip finalization when action is no_action_needed', async () => {
      const paymentResult: MakePaymentResult = {
        success: false,
        reason: 'No action needed',
        action: 'no_action_needed',
      };

      await activities.finalizeTransaction(
        transactionId,
        paymentResult,
        'immediate' as MakeZaiPaymentWorkflowInput['releasePolicy']
      );

      expect(transactionsRepository.getTransactionById.called).to.be.false;
    });

    it('should mark transaction as completed for payment_success', async () => {
      const paymentResult: MakePaymentResult = {
        success: true,
        reason: 'Payment successful',
        action: 'payment_success',
      };

      await activities.finalizeTransaction(
        transactionId,
        paymentResult,
        'immediate' as MakeZaiPaymentWorkflowInput['releasePolicy']
      );

      expect(transactionService.markCompleted.calledOnce).to.be.true;
    });

    it('should mark transaction as failed for payment_failed', async () => {
      const paymentResult: MakePaymentResult = {
        success: false,
        reason: 'Payment declined',
        action: 'payment_failed',
        code: 'card_declined',
      };

      await activities.finalizeTransaction(
        transactionId,
        paymentResult,
        'immediate' as MakeZaiPaymentWorkflowInput['releasePolicy']
      );

      expect(transactionService.markFailed.calledOnce).to.be.true;
      expect(transactionService.markFailed.firstCall.args[0]).to.deep.include({
        transactionId,
        declineReason: 'Payment declined',
        declineCode: 'card_declined',
      });
    });

    it('should sync state and mark completed for sync_state with completed state', async () => {
      const paymentResult: MakePaymentResult = {
        success: true,
        reason: 'Transaction completed',
        action: 'sync_state',
        state: states.completed,
      };

      await activities.finalizeTransaction(
        transactionId,
        paymentResult,
        'immediate' as MakeZaiPaymentWorkflowInput['releasePolicy']
      );

      expect(transactionService.markCompleted.calledOnce).to.be.true;
      expect(transactionsRepository.updateTransaction.calledOnce).to.be.true;
    });

    it('should sync state and mark failed for sync_state with failed state', async () => {
      const paymentResult: MakePaymentResult = {
        success: false,
        reason: 'Transaction failed',
        action: 'sync_state',
        state: states.failed,
      };

      await activities.finalizeTransaction(
        transactionId,
        paymentResult,
        'immediate' as MakeZaiPaymentWorkflowInput['releasePolicy']
      );

      expect(transactionService.markFailed.calledOnce).to.be.true;
      expect(transactionsRepository.updateTransaction.calledOnce).to.be.true;
    });

    it('should return early when transaction not found', async () => {
      transactionsRepository.getTransactionById.resolves(null);
      const paymentResult: MakePaymentResult = {
        success: true,
        reason: 'Payment successful',
        action: 'payment_success',
      };

      await activities.finalizeTransaction(
        transactionId,
        paymentResult,
        'immediate' as MakeZaiPaymentWorkflowInput['releasePolicy']
      );

      expect(transactionService.markCompleted.called).to.be.false;
    });

    it('should return early when seller not found', async () => {
      userRepository.getUserById.reset();
      userRepository.getUserById.resolves(null);
      const paymentResult: MakePaymentResult = {
        success: true,
        reason: 'Payment successful',
        action: 'payment_success',
      };

      await activities.finalizeTransaction(
        transactionId,
        paymentResult,
        'immediate' as MakeZaiPaymentWorkflowInput['releasePolicy']
      );

      expect(transactionService.markCompleted.called).to.be.false;
    });

    it('should handle database errors during finalization', async () => {
      const dbError = new SequelizeError(new Error('Database error'));
      transactionsRepository.getTransactionById.rejects(dbError);
      const paymentResult: MakePaymentResult = {
        success: true,
        reason: 'Payment successful',
        action: 'payment_success',
      };

      try {
        await activities.finalizeTransaction(
          transactionId,
          paymentResult,
          'immediate' as MakeZaiPaymentWorkflowInput['releasePolicy']
        );
        expect.fail('Expected error to be thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(ApplicationFailure);
        expect((error as ApplicationFailure).nonRetryable).to.be.false;
      }
    });

    it('should work with delayed release policy', async () => {
      const paymentResult: MakePaymentResult = {
        success: true,
        reason: 'Payment successful',
        action: 'payment_success',
      };

      await activities.finalizeTransaction(
        transactionId,
        paymentResult,
        'delayed' as MakeZaiPaymentWorkflowInput['releasePolicy']
      );

      expect(transactionService.markCompleted.calledOnce).to.be.true;
    });

    it('should handle sync_state with payment_deposited state', async () => {
      const paymentResult: MakePaymentResult = {
        success: true,
        reason: 'Payment deposited',
        action: 'sync_state',
        state: states.payment_deposited,
      };

      await activities.finalizeTransaction(
        transactionId,
        paymentResult,
        'immediate' as MakeZaiPaymentWorkflowInput['releasePolicy']
      );

      expect(transactionService.markCompleted.calledOnce).to.be.false;
      expect(transactionsRepository.updateTransaction.calledOnce).to.be.true;
    });

    it('should handle sync_state with pending state (no completion or failure)', async () => {
      const paymentResult: MakePaymentResult = {
        success: true,
        reason: 'Still pending',
        action: 'sync_state',
        state: states.pending,
      };

      await activities.finalizeTransaction(
        transactionId,
        paymentResult,
        'immediate' as MakeZaiPaymentWorkflowInput['releasePolicy']
      );

      expect(transactionService.markCompleted.called).to.be.false;
      expect(transactionService.markFailed.called).to.be.false;
      expect(transactionsRepository.updateTransaction.calledOnce).to.be.true;
    });

    it('should calculate release date correctly for immediate release', async () => {
      const paymentResult: MakePaymentResult = {
        success: true,
        reason: 'Payment successful',
        action: 'payment_success',
      };

      await activities.finalizeTransaction(
        transactionId,
        paymentResult,
        'immediate' as MakeZaiPaymentWorkflowInput['releasePolicy']
      );

      expect(transactionService.markCompleted.calledOnce).to.be.true;
      const markCompletedArgs =
        transactionService.markCompleted.firstCall.args[0];
      expect(markCompletedArgs.transactionId).to.equal(transactionId);
      expect(markCompletedArgs.receivedAt).to.be.a('string');
      expect(markCompletedArgs.releaseAt).to.be.a('string');
    });
  });

  describe('finalizeTransaction', () => {
    const mockSeller = userFactory.build({ id: 'seller123' });

    beforeEach(() => {
      transactionsRepository.getTransactionById.resolves(mockTransaction);
      userRepository.getUserById.resolves(mockSeller);
    });

    it('should skip finalization when action is no_action_needed', async () => {
      const paymentResult: MakePaymentResult = {
        success: false,
        reason: 'No action needed',
        action: 'no_action_needed',
      };

      await activities.finalizeTransaction(
        transactionId,
        paymentResult,
        'immediate' as MakeZaiPaymentWorkflowInput['releasePolicy']
      );

      expect(transactionsRepository.getTransactionById.called).to.be.false;
    });

    it('should mark transaction as completed for payment_success', async () => {
      const paymentResult: MakePaymentResult = {
        success: true,
        reason: 'Payment successful',
        action: 'payment_success',
      };

      await activities.finalizeTransaction(
        transactionId,
        paymentResult,
        'immediate' as MakeZaiPaymentWorkflowInput['releasePolicy']
      );

      expect(transactionService.markCompleted.calledOnce).to.be.true;
    });

    it('should mark transaction as failed for payment_failed', async () => {
      const paymentResult: MakePaymentResult = {
        success: false,
        reason: 'Payment declined',
        action: 'payment_failed',
        code: 'card_declined',
      };

      await activities.finalizeTransaction(
        transactionId,
        paymentResult,
        'immediate' as MakeZaiPaymentWorkflowInput['releasePolicy']
      );

      expect(transactionService.markFailed.calledOnce).to.be.true;
      expect(transactionService.markFailed.firstCall.args[0]).to.deep.include({
        transactionId,
        declineReason: 'Payment declined',
        declineCode: 'card_declined',
      });
    });

    it('should sync state and mark completed for sync_state with completed state', async () => {
      const paymentResult: MakePaymentResult = {
        success: true,
        reason: 'Transaction completed',
        action: 'sync_state',
        state: states.completed,
      };

      await activities.finalizeTransaction(
        transactionId,
        paymentResult,
        'immediate' as MakeZaiPaymentWorkflowInput['releasePolicy']
      );

      expect(transactionService.markCompleted.calledOnce).to.be.true;
      expect(transactionsRepository.updateTransaction.calledOnce).to.be.true;
    });

    it('should sync state and mark failed for sync_state with failed state', async () => {
      const paymentResult: MakePaymentResult = {
        success: false,
        reason: 'Transaction failed',
        action: 'sync_state',
        state: states.failed,
      };

      await activities.finalizeTransaction(
        transactionId,
        paymentResult,
        'immediate' as MakeZaiPaymentWorkflowInput['releasePolicy']
      );

      expect(transactionService.markFailed.calledOnce).to.be.true;
      expect(transactionsRepository.updateTransaction.calledOnce).to.be.true;
    });

    it('should return early when transaction not found', async () => {
      transactionsRepository.getTransactionById.resolves(null);
      const paymentResult: MakePaymentResult = {
        success: true,
        reason: 'Payment successful',
        action: 'payment_success',
      };

      await activities.finalizeTransaction(
        transactionId,
        paymentResult,
        'immediate' as MakeZaiPaymentWorkflowInput['releasePolicy']
      );

      expect(transactionService.markCompleted.called).to.be.false;
    });

    it('should return early when seller not found', async () => {
      userRepository.getUserById.reset();
      userRepository.getUserById.resolves(null);
      const paymentResult: MakePaymentResult = {
        success: true,
        reason: 'Payment successful',
        action: 'payment_success',
      };

      await activities.finalizeTransaction(
        transactionId,
        paymentResult,
        'immediate' as MakeZaiPaymentWorkflowInput['releasePolicy']
      );

      expect(transactionService.markCompleted.called).to.be.false;
    });

    it('should handle database errors during finalization', async () => {
      const dbError = new SequelizeError(new Error('Database error'));
      transactionsRepository.getTransactionById.rejects(dbError);
      const paymentResult: MakePaymentResult = {
        success: true,
        reason: 'Payment successful',
        action: 'payment_success',
      };

      try {
        await activities.finalizeTransaction(
          transactionId,
          paymentResult,
          'immediate' as MakeZaiPaymentWorkflowInput['releasePolicy']
        );
        expect.fail('Expected error to be thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(ApplicationFailure);
        expect((error as ApplicationFailure).nonRetryable).to.be.false;
      }
    });

    it('should work with delayed release policy', async () => {
      const paymentResult: MakePaymentResult = {
        success: true,
        reason: 'Payment successful',
        action: 'payment_success',
      };

      await activities.finalizeTransaction(
        transactionId,
        paymentResult,
        'delayed' as MakeZaiPaymentWorkflowInput['releasePolicy']
      );

      expect(transactionService.markCompleted.calledOnce).to.be.true;
    });

    it('should handle sync_state with payment_deposited state', async () => {
      const paymentResult: MakePaymentResult = {
        success: true,
        reason: 'Payment deposited',
        action: 'sync_state',
        state: states.payment_deposited,
      };

      await activities.finalizeTransaction(
        transactionId,
        paymentResult,
        'immediate' as MakeZaiPaymentWorkflowInput['releasePolicy']
      );

      expect(transactionService.markCompleted.calledOnce).to.be.false;
      expect(transactionsRepository.updateTransaction.calledOnce).to.be.true;
    });

    it('should handle sync_state with pending state (no completion or failure)', async () => {
      const paymentResult: MakePaymentResult = {
        success: true,
        reason: 'Still pending',
        action: 'sync_state',
        state: states.pending,
      };

      await activities.finalizeTransaction(
        transactionId,
        paymentResult,
        'immediate' as MakeZaiPaymentWorkflowInput['releasePolicy']
      );

      expect(transactionService.markCompleted.called).to.be.false;
      expect(transactionService.markFailed.called).to.be.false;
      expect(transactionsRepository.updateTransaction.calledOnce).to.be.true;
    });

    it('should calculate release date correctly for immediate release', async () => {
      const paymentResult: MakePaymentResult = {
        success: true,
        reason: 'Payment successful',
        action: 'payment_success',
      };

      await activities.finalizeTransaction(
        transactionId,
        paymentResult,
        'immediate' as MakeZaiPaymentWorkflowInput['releasePolicy']
      );

      expect(transactionService.markCompleted.calledOnce).to.be.true;
      const markCompletedArgs =
        transactionService.markCompleted.firstCall.args[0];
      expect(markCompletedArgs.transactionId).to.equal(transactionId);
      expect(markCompletedArgs.receivedAt).to.be.a('string');
      expect(markCompletedArgs.releaseAt).to.be.a('string');
    });

    it('should call performSideEffectsForLegacyWorkflows for all action types', async () => {
      const testCases: MakePaymentResult[] = [
        {
          success: true,
          reason: 'Payment successful',
          action: 'payment_success',
        },
        {
          success: false,
          reason: 'Payment failed',
          action: 'payment_failed',
          code: 'insufficient_funds',
        },
        {
          success: true,
          reason: 'State synced',
          action: 'sync_state',
          state: states.failed,
        },
      ];

      for (const paymentResult of testCases) {
        // Reset stubs before each test case
        transactionService.performSideEffectsForLegacyWorkflows.reset();
        transactionService.markCompleted.reset();
        transactionService.markFailed.reset();
        transactionsRepository.updateTransaction.reset();

        await activities.finalizeTransaction(
          transactionId,
          paymentResult,
          'immediate' as MakeZaiPaymentWorkflowInput['releasePolicy']
        );

        expect(
          transactionService.performSideEffectsForLegacyWorkflows.calledOnce,
          `performSideEffectsForLegacyWorkflows should be called for action: ${paymentResult.action}`
        ).to.be.true;
        expect(
          transactionService.performSideEffectsForLegacyWorkflows.firstCall
            .args[0]
        ).to.deep.equal({ transactionId });
      }
    });

    it('should not include declineCode for sync_state failures', async () => {
      const paymentResult: MakePaymentResult = {
        success: false,
        reason: 'Transaction failed',
        action: 'sync_state',
        state: states.failed,
      };

      await activities.finalizeTransaction(
        transactionId,
        paymentResult,
        'immediate' as MakeZaiPaymentWorkflowInput['releasePolicy']
      );

      expect(transactionService.markFailed.calledOnce).to.be.true;
      const markFailedArgs = transactionService.markFailed.firstCall.args[0];
      expect(markFailedArgs).to.have.property(
        'declineReason',
        'Transaction failed'
      );
      expect(markFailedArgs.declineCode).to.be.undefined;
    });

    it('should handle unexpected action types gracefully', async () => {
      // This tests the default case in the switch statement
      const paymentResult: any = {
        success: true,
        reason: 'Unknown action',
        action: 'unknown_action',
      };

      await activities.finalizeTransaction(
        transactionId,
        paymentResult,
        'immediate' as MakeZaiPaymentWorkflowInput['releasePolicy']
      );

      // Should not mark as completed or failed
      expect(transactionService.markCompleted.called).to.be.false;
      expect(transactionService.markFailed.called).to.be.false;
      // But should still call side effects
      expect(transactionService.performSideEffectsForLegacyWorkflows.calledOnce)
        .to.be.true;
    });

    it('should handle transaction errors and wrap them appropriately', async () => {
      const sequelizeError = new SequelizeError(
        new Error('Database connection lost')
      );
      transactionService.markCompleted.rejects(sequelizeError);

      const paymentResult: MakePaymentResult = {
        success: true,
        reason: 'Payment successful',
        action: 'payment_success',
      };

      await expect(
        activities.finalizeTransaction(
          transactionId,
          paymentResult,
          'immediate' as MakeZaiPaymentWorkflowInput['releasePolicy']
        )
      ).to.be.rejectedWith(ApplicationFailure);
    });

    it('should rethrow ApplicationFailure without wrapping', async () => {
      const appFailure = ApplicationFailure.nonRetryable('Custom failure');
      transactionService.markCompleted.rejects(appFailure);

      const paymentResult: MakePaymentResult = {
        success: true,
        reason: 'Payment successful',
        action: 'payment_success',
      };

      await expect(
        activities.finalizeTransaction(
          transactionId,
          paymentResult,
          'immediate' as MakeZaiPaymentWorkflowInput['releasePolicy']
        )
      ).to.be.rejectedWith(appFailure);
    });
  });
});
