import { defineConfig } from '@playwright/test';

export default defineConfig({
  testDir: './test_e2e',
  fullyParallel: false,
  forbidOnly: <PERSON><PERSON><PERSON>(process.env.CI),
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'list',
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    launchOptions: {
      args: ['--require', 'ts-node/register'],
    },
  },
  projects: [
    {
      name: 'api',
      testMatch: /.*\.e2e\.ts/,
    },
  ],
});
