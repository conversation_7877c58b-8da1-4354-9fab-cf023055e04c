import { expect, test } from '@playwright/test';
import { uuid4 } from '@temporalio/workflow';
import { v4 as uuidv4 } from 'uuid';
import { getUpfrontHttp } from './http/upfront.http';
import { getTransactionHttp } from './http/transaction.http';
import { UpfrontFlowStep } from '../src/types/upfront.types';
import { waitForUpfrontToBeInState } from './utils/wait_for.util';
import {
  e2eBuyerCardPaymentMethodId,
  e2eBuyerId,
  e2eSellerId,
} from '../config/e2e';
import { states } from '../src/models/transaction';
import './utils/matchers.util';

test.describe.serial('Upfront Payment Controller E2E', () => {
  const endpointUrl = 'http://localhost:4000';
  const upfrontHttp = getUpfrontHttp(endpointUrl);
  const transactionHttp = getTransactionHttp(endpointUrl);

  /*
   * ============================================================================
   * Tests for upfront payments where final amount is EQUAL to initial payment
   * Verifies no additional transaction is needed when buyer paid exactly the holding amount
   * ============================================================================
   */
  test.describe('Upfront payment with same final amount', () => {
    const orderId = uuidv4();
    const invoiceId = uuid4();
    const reference = `test_${Date.now()}`;
    let holdingTransactionId: string;

    test('should create an upfront payment', async () => {
      // ACT
      holdingTransactionId = (
        await upfrontHttp.createUpfrontPayment({
          amountCents: 1000,
          description: reference,
          sellerUserId: e2eSellerId,
          buyerUserId: e2eBuyerId,
          orderId,
          invoiceId,
          userId: e2eBuyerId,
          paymentMethodId: e2eBuyerCardPaymentMethodId,
        })
      ).holdingTransactionId;

      await waitForUpfrontToBeInState(
        upfrontHttp,
        orderId,
        UpfrontFlowStep.WaitingForFinaliseSignal
      );

      const holdingTx = await transactionHttp.getTransaction(
        holdingTransactionId
      );

      // ASSERT
      expect(holdingTx.amount).toBe(1000);
      expect(holdingTx.fee).toBe('0.17');
      expect(holdingTx.state).toBe(states.completed);
      expect(holdingTx.release_at).toBeUndefined();
      expect(holdingTx.relatedTransactions?.length).toBe(0);
      expect(holdingTx.orderIds).toEqual([orderId]);
      expect(holdingTx.invoiceIds[0]).toEqual(invoiceId);
    });

    test('should finalize an upfront payment with same amount', async () => {
      // ACT
      const response = await upfrontHttp.finalizeUpfrontPayment({
        orderId,
        finalAmountCents: 1000,
        description: `Finalise ${reference}`,
      });

      await waitForUpfrontToBeInState(
        upfrontHttp,
        orderId,
        UpfrontFlowStep.Finished
      );

      const holdingTx = await transactionHttp.getTransaction(
        holdingTransactionId
      );

      // ASSERT
      expect(response).toEqual({}); // If the final amount equals the holding amount, no additional transaction is needed
      expect(holdingTx.release_at).toBeDefined();
      expect(holdingTx.release_at).toBeInThePast();
      expect(holdingTx.orderIds).toEqual([orderId]);
      expect(holdingTx.invoiceIds[0]).toEqual(invoiceId);
      expect(holdingTx.relatedTransactions?.length).toBe(0);
    });
  });

  /*
   * ============================================================================
   * Tests for upfront payments where final amount is GREATER than initial payment
   * Verifies top up flow works correctly when buyer underpaid initially
   * ============================================================================
   */
  test.describe('Upfront payment requiring a top up', () => {
    const orderId = uuidv4();
    const invoiceId = uuid4();
    const reference = `test_${Date.now()}`;
    let holdingTransactionId: string;

    test('should create an upfront payment', async () => {
      // ACT
      holdingTransactionId = (
        await upfrontHttp.createUpfrontPayment({
          amountCents: 1000,
          description: reference,
          sellerUserId: e2eSellerId,
          buyerUserId: e2eBuyerId,
          orderId,
          invoiceId,
          userId: e2eBuyerId,
          paymentMethodId: e2eBuyerCardPaymentMethodId,
        })
      ).holdingTransactionId;

      await waitForUpfrontToBeInState(
        upfrontHttp,
        orderId,
        UpfrontFlowStep.WaitingForFinaliseSignal
      );

      const holdingTx = await transactionHttp.getTransaction(
        holdingTransactionId
      );

      // ASSERT
      expect(holdingTx.amount).toBe(1000);
      expect(holdingTx.fee).toBe('0.17');
      expect(holdingTx.state).toBe(states.completed);
      expect(holdingTx.release_at).toBeUndefined();
      expect(holdingTx.relatedTransactions?.length).toBe(0);
      expect(holdingTx.buyer_id).toBe(e2eBuyerId);
      expect(holdingTx.seller_id).toBe(e2eSellerId);
      expect(holdingTx.invoiceIds[0]).toEqual(invoiceId);
      expect(holdingTx.orderIds).toEqual([orderId]);
    });

    test('should finalize and get a top up to settle the payment', async () => {
      // ACT
      const response = await upfrontHttp.finalizeUpfrontPayment({
        orderId,
        finalAmountCents: 1500,
        description: `Finalise ${reference}`,
      });

      await waitForUpfrontToBeInState(
        upfrontHttp,
        orderId,
        UpfrontFlowStep.Finished
      );

      const holdingTx = await transactionHttp.getTransaction(
        holdingTransactionId
      );

      const topupTx = await transactionHttp.getTransaction(
        response.finalisedTransactionId
      );

      // ASSERT
      expect(holdingTx.release_at).toBeDefined();
      expect(holdingTx.release_at).toBeInThePast();

      expect(topupTx.amount).toBe(500);
      expect(topupTx.fee).toBe('0.08');
      expect(topupTx.state).toBe(states.completed);
      expect(topupTx.relatedTransactions?.length).toBe(1);
      expect(topupTx.relatedTransactions?.[0]).toBe(holdingTransactionId);
      expect(topupTx.release_at).toBeInThePast();
      expect(topupTx.orderIds).toEqual([orderId]);
      expect(topupTx.invoiceIds[0]).toEqual(invoiceId);
    });
  });

  /*
   * ============================================================================
   * Tests for upfront payments where final amount is LESS than initial payment
   * Verifies refund flow works correctly when buyer overpaid initially
   * ============================================================================
   */
  test.describe('Upfront payment requiring a refund of difference to buyer', () => {
    const orderId = uuidv4();
    const invoiceId = uuid4();
    const reference = `test_${Date.now()}`;
    let holdingTransactionId: string;

    test('should create an upfront payment', async () => {
      // ACT
      holdingTransactionId = (
        await upfrontHttp.createUpfrontPayment({
          amountCents: 1000,
          description: reference,
          sellerUserId: e2eSellerId,
          buyerUserId: e2eBuyerId,
          orderId,
          invoiceId,
          userId: e2eBuyerId,
          paymentMethodId: e2eBuyerCardPaymentMethodId,
        })
      ).holdingTransactionId;

      await waitForUpfrontToBeInState(
        upfrontHttp,
        orderId,
        UpfrontFlowStep.WaitingForFinaliseSignal
      );

      const holdingTx = await transactionHttp.getTransaction(
        holdingTransactionId
      );

      // ASSERT
      expect(holdingTx.amount).toBe(1000);
      expect(holdingTx.fee).toBe('0.17');
      expect(holdingTx.state).toBe(states.completed);
      expect(holdingTx.release_at).toBeUndefined();
      expect(holdingTx.relatedTransactions?.length).toBe(0);
      expect(holdingTx.buyer_id).toBe(e2eBuyerId);
      expect(holdingTx.seller_id).toBe(e2eSellerId);
      expect(holdingTx.invoiceIds[0]).toEqual(invoiceId);
      expect(holdingTx.orderIds).toEqual([orderId]);
    });

    test('should finalize and refund the user the difference', async () => {
      // ACT
      const response = await upfrontHttp.finalizeUpfrontPayment({
        orderId,
        finalAmountCents: 800,
        description: `Finalise ${reference}`,
      });

      await waitForUpfrontToBeInState(
        upfrontHttp,
        orderId,
        UpfrontFlowStep.Finished
      );

      const holdingTx = await transactionHttp.getTransaction(
        holdingTransactionId
      );

      const refundTx = await transactionHttp.getTransaction(
        response.finalisedTransactionId
      );

      // ASSERT
      expect(holdingTx.state).toBe(states.completed);
      expect(holdingTx.release_at).toBeDefined();
      expect(holdingTx.release_at).toBeInThePast();
      expect(holdingTx.relatedTransactions?.length).toBe(0);

      expect(refundTx.amount).toBe(200);
      expect(refundTx.fee).toBe('0.00');
      expect(refundTx.state).toBe(states.completed);
      expect(refundTx.relatedTransactions?.length).toBe(1);
      expect(refundTx.relatedTransactions?.[0]).toBe(holdingTransactionId);
      expect(refundTx.release_at).toBeInThePast();
      expect(refundTx.orderIds).toEqual([orderId]);
      expect(refundTx.invoiceIds[0]).toEqual(invoiceId);
    });
  });

  /*
   * ============================================================================
   * Test that a failed credit card payment will result in the transaction being
   * put into a failed state and the OM service notified that the transaction
   * has failed.
   * ============================================================================
   */
  // TODO: Revise this test as it's just not correctly testing the failed state
  // test.describe('Upfront payment with failed credit card payment', () => {
  //   const orderId = uuidv4();
  //   const reference = `test_${Date.now()}`;

  //   test('should put the transaction into a failed state and notify the OM service', async () => {
  //     // ACT
  //     const invalidPaymentMethodId = 'invalid-payment-method-id';

  //     await expect(async () => {
  //       await upfrontHttp.createUpfrontPayment({
  //         amountCents: 1000,
  //         description: reference,
  //         sellerUserId: e2eSellerId,
  //         buyerUserId: e2eBuyerId,
  //         orderId,
  //         invoiceIds: [],
  //         userId: e2eBuyerId,
  //         paymentMethodId: invalidPaymentMethodId,
  //       });
  //     }).rejects.toThrow();
  //   });
  // });
});
