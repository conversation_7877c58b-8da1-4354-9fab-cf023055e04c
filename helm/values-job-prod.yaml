replicaCount: 4
nameOverride: payments
image:
  repository: ************.dkr.ecr.ap-southeast-2.amazonaws.com/ordermentum/payments
  tag: master
  pullPolicy: IfNotPresent
service:
  type: ClusterIP
  externalPort: 9464
  internalPort: 9464 # This should match the PORT value in the env section
  enabled: true
disableLivenessProbe: true
disableReadinessProbe: true
resources:
  limits:
    #  cpu: 100m
    memory: 768Mi
  requests:
    #  cpu: 100m
    memory: 512Mi
serviceAccount: aws-s3-sqs-access-v2
env:
  awsRegion:
    name: 'AWS_REGION'
    value: 'ap-southeast-2'
  awsBucketName:
    name: 'AWS_BUCKET_NAME'
    value: 'exports.ordermentum.io'
  callbackUri:
    name: 'CALLBACK_URI'
    value: 'https://payments.ordermentum.com/'
  logLevel:
    name: 'LOG_LEVEL'
    value: 'info'
  nodeEnv:
    name: 'NODE_ENV'
    value: 'production'
  disableAuth:
    name: 'DISABLE_AUTH'
    value: 'false'
  port:
    name: 'PORT'
    value: 9464
  possibleFailureDays:
    name: 'POSSIBLE_FAILURE_DAYS'
    value: 4
  ppSandpit:
    name: 'PP_SANDPIT'
    value: 'false'
  skipBackends:
    name: 'SKIP_BACKENDS'
    value: 'false'
  tz:
    name: 'TZ'
    value: 'Australia/Sydney'
  redisUrl:
    name: 'REDIS_URL'
    value: 'master.redis-main.oeeadg.apse2.cache.amazonaws.com'
  redisUseTls:
    name: 'REDIS_USE_TLS'
    value: 'true'
  elasticsearchHost:
    name: 'ELASTICSEARCH_HOST'
    value: 'http://es-data.production.ordermentum.com:9201'
  steveoEngine:
    name: 'STEVEO_ENGINE'
    value: 'sqs'
  holdingUserId:
    name: 'OM_HOLDING_USER_ID'
    value: '2f5bb462-60ac-ba99-9e64-cbfbf693d161'
  fundingUserId:
    name: 'OM_FUNDING_USER_ID'
    value: '1395f0c7-fe41-28f4-a0de-9f22dc9e4ba6'
  refundUserId:
    name: 'OM_REFUND_USER_ID'
    value: '09cd653e-7c64-2fa3-ff1a-76769cff4158'
  dbPoolSize:
    name: 'DB_POOL_SIZE'
    value: 10
  defaultJobLag:
    name: 'DEFAULT_JOB_LAG'
    value: '5000'
  jobEnqueueLimit:
    name: 'JOB_ENQUEUE_LIMIT'
    value: 5
  workerCount:
    name: 'WORKER_COUNT'
    value: 5
  sqsMaxMessages:
    name: 'SQS_MAX_MESSAGES'
    value: 5
  asapPublicKeysUrl:
    name: 'ASAP_PUBLIC_KEYS_URL'
    value: 'https://ordermentum-service-public-keys-production.s3.ap-southeast-2.amazonaws.com/'
  baseUrl:
    name: 'OM_BASE_URL'
    value: 'https://app.ordermentum.com'
  insightsUrl:
    name: 'INSIGHTS_URL'
    value: 'https://insights.ordermentum.com'
  fundingLowBalanceThreshold:
    name: 'FUNDING_LOW_BALANCE_THRESHOLD'
    value: 200000
  doubleEntryJournal:
    name: 'DOUBLE_ENTRY_JOURNAL'
    value: 'true'
  journalV3:
    name: 'JOURNAL_V3'
    value: 'true'
  asapUserId:
    name: 'ASAP_USER_ID'
    value: '6f955320-d486-46b9-a45d-7f0ac3760d2c'
  datadogEnvironment:
    name: DD_ENV
    value: production
  datadogServiceName:
    name: DD_SERVICE
    value: payments-job
  datadogServiceVersion:
    name: DD_VERSION
    value: ''
  datadogEntityId:
    name: DD_ENTITY_ID
    valueFrom:
      fieldRef:
        apiVersion: v1
        fieldPath: metadata.uid
  nodeOptions:
    name: NODE_OPTIONS
    value: ' --require=dd-trace/init'
  finstroAPIUrl:
    name: 'FINSTRO_API_URL'
    value: 'https://coreapi.prod-au.finstro.com/api/SDK'
  authUrl:
    name: 'AUTH_URL'
    value: 'https://auth.ordermentum.com'
  notificationsUrl:
    name: 'NOTIFICATIONS_URL'
    value: 'https://notifications.ordermentum.com'
  publicRoutesEnabled:
    name: 'RETAILER_ROUTES_ENABLED'
    value: 'true'
  indexingEnabled:
    name: 'INDEXING_ENABLED'
    value: 'true'
  elasticSearchUser:
    name: 'ELASTICSEARCH_USER'
    value: 'elastic'
  elasticSearchPass:
    name: 'ELASTICSEARCH_PASS'
    value: 'changeme'
  elasticSearchPrefix:
    name: 'ELASTICSEARCH_PREFIX'
    value: 'production'
  finstroUpfrontEnabled:
    name: 'FINSTRO_UPFRONT_ENABLED'
    value: 'true'
  enabledWorkers:
    name: ENABLED_WORKERS
    value: 'temporal,sqs,database,kafka'
  temporalQueues:
    name: TEMPORAL_QUEUES
    value: 'payments:upfront-payment,payments:generic,payments:promotional-payments'
externalSecrets:
  - AUTH_PASS
  - AUTH_USER
  - SLACK_TOKEN
  - DATABASE_URI
  - OM_USER_ID
  - PP_TOKEN
  - AP_CLIENT_ID
  - AP_CLIENT_SECRET
  - AP_CLIENT_SCOPE
  - PP_USER
  - MANDRILL_TOKEN
  - SENTRY_DSN
  - SPREEDLY_ENVIRONMENT
  - SPREEDLY_TOKEN
  - STRIPE_SECRET_KEY
  - STRIPE_SIGNING_SECRET
  - INTEGRATION_WEBHOOK
  - KAFKA_BOOTSTRAP_SERVERS
  - ASAP_PUBLIC_KEY_ID
  - ASAP_PRIVATE_KEY
  - OM_WALLET_USER_ID
  - FEES_ACCOUNT_ID
  - REDIS_PASSWORD
  - OM_FINSTRO_USER_ID
  - FINSTRO_SDK_TOKEN
  - JWT_SECRET
secretsManagerSecretName: payments
mixedHPA:
  enabled: true
  spec:
    pollingInterval: 15
    minReplicaCount: 2
    maxReplicaCount: 30
    triggers:
      - type: temporal
        taskQueue: "payments:generic"
        endpoint: "production.qefiw.tmprl.cloud:7233"
        namespace: "production.qefiw"
        queueTypes: "workflow,activity"
        targetQueueSize: 1
      - type: temporal
        taskQueue: "payments:upfront-payment"
        endpoint: "production.qefiw.tmprl.cloud:7233"
        namespace: "production.qefiw"
        queueTypes: "workflow,activity"
        targetQueueSize: 1
      - type: temporal
        taskQueue: "payments:promotional-payments"
        endpoint: "production.qefiw.tmprl.cloud:7233"
        namespace: "production.qefiw"
        queueTypes: "workflow,activity"
        targetQueueSize: 1
      - type: aws-sqs-queue
        queueURL: 'https://sqs.ap-southeast-2.amazonaws.com/123564074355/PRODUCTION_PAYMENTS_BATCH'
        awsRegion: 'ap-southeast-2'
        queueLength: '300'
      - type: aws-sqs-queue
        queueURL: 'https://sqs.ap-southeast-2.amazonaws.com/123564074355/PRODUCTION_PAYMENTS_BATCH-CHECK'
        awsRegion: 'ap-southeast-2'
        queueLength: '300'
      - type: aws-sqs-queue
        queueURL: 'https://sqs.ap-southeast-2.amazonaws.com/123564074355/PRODUCTION_PAYMENTS_BATCH-DISBURSEMENT'
        awsRegion: 'ap-southeast-2'
        queueLength: '300'
      - type: aws-sqs-queue
        queueURL: 'https://sqs.ap-southeast-2.amazonaws.com/123564074355/PRODUCTION_PAYMENTS_BATCH-NOTIFICATION'
        awsRegion: 'ap-southeast-2'
        queueLength: '300'
      - type: aws-sqs-queue
        queueURL: 'https://sqs.ap-southeast-2.amazonaws.com/123564074355/PRODUCTION_PAYMENTS_BATCH-TRANSACTIONS'
        awsRegion: 'ap-southeast-2'
        queueLength: '300'
      - type: aws-sqs-queue
        queueURL: 'https://sqs.ap-southeast-2.amazonaws.com/123564074355/PRODUCTION_PAYMENTS_BULK-TRANSACTION-CHECK'
        awsRegion: 'ap-southeast-2'
        queueLength: '300'
      - type: aws-sqs-queue
        queueURL: 'https://sqs.ap-southeast-2.amazonaws.com/123564074355/PRODUCTION_PAYMENTS_DISBURSEMENT'
        awsRegion: 'ap-southeast-2'
        queueLength: '300'
      - type: aws-sqs-queue
        queueURL: 'https://sqs.ap-southeast-2.amazonaws.com/123564074355/PRODUCTION_PAYMENTS_NOTIFICATION'
        awsRegion: 'ap-southeast-2'
        queueLength: '150'
      - type: aws-sqs-queue
        queueURL: 'https://sqs.ap-southeast-2.amazonaws.com/123564074355/PRODUCTION_PAYMENTS_PAYMENT-RUN-CREATE'
        awsRegion: 'ap-southeast-2'
        queueLength: '300'
      - type: aws-sqs-queue
        queueURL: 'https://sqs.ap-southeast-2.amazonaws.com/123564074355/PRODUCTION_PAYMENTS_PRODUCTION_TRANSACTION-CHANGED'
        awsRegion: 'ap-southeast-2'
        queueLength: '300'
      - type: aws-sqs-queue
        queueURL: 'https://sqs.ap-southeast-2.amazonaws.com/123564074355/PRODUCTION_PAYMENTS_REFUND-CHECK'
        awsRegion: 'ap-southeast-2'
        queueLength: '300'
      - type: aws-sqs-queue
        queueURL: 'https://sqs.ap-southeast-2.amazonaws.com/123564074355/PRODUCTION_PAYMENTS_TEST'
        awsRegion: 'ap-southeast-2'
        queueLength: '300'
      - type: aws-sqs-queue
        queueURL: 'https://sqs.ap-southeast-2.amazonaws.com/123564074355/PRODUCTION_PAYMENTS_TRANSACTION-CHECK'
        awsRegion: 'ap-southeast-2'
        queueLength: '300'
      - type: aws-sqs-queue
        queueURL: 'https://sqs.ap-southeast-2.amazonaws.com/123564074355/PRODUCTION_PAYMENTS_TRANSACTIONS-SYNC'
        awsRegion: 'ap-southeast-2'
        queueLength: '300'
      - type: aws-sqs-queue
        queueURL: 'https://sqs.ap-southeast-2.amazonaws.com/123564074355/PRODUCTION_PAYMENTS_WALLET-CHECK'
        awsRegion: 'ap-southeast-2'
        queueLength: '300'
      - type: aws-sqs-queue
        queueURL: 'https://sqs.ap-southeast-2.amazonaws.com/123564074355/PRODUCTION_PAYMENTS_WEBHOOK'
        awsRegion: 'ap-southeast-2'
        queueLength: '300'
deployment:
  podLabels:
    tags.datadoghq.com/env: production
    tags.datadoghq.com/service: payments-job
  additionalPodAnnotations:
    # Datadog annotations for OpenMetrics autodiscovery
    ad.datadoghq.com/payments-job.checks: |
      {
        "openmetrics": {
          "init_config": {},
          "instances": [
            {
              "openmetrics_endpoint": "http://%%host%%:%%port%%/metrics",
              "namespace": "temporal",
              "metrics": [".*"]
            }
          ]
        }
      }
  command:
    - yarn
    - run
    - jobs
