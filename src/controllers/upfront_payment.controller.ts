import { getTemporalClient } from '@ordermentum/temporal';
import Logger from 'bunyan';
import {
  UpfrontPayment,
  UpfrontPaymentComplete,
  CancelUpfrontPayment,
} from '../ng/common/requests/upfront_payments.request';
import { logger } from '../lib/logger';
import upfrontPaymentWorkflowDefinition from '../ng/workflow/definitions/upfront_payment.workflow';
import { UpfrontFlowStep } from '../types/upfront.types';
import { count } from '../lib/stats';
import { WorkflowFactory } from '../ng/workflow/workflow.factory';

/**
 * The controller for the upfront payment workflow
 */
export const UpfrontPaymentController = {
  /**
   * Create an upfront payment workflow
   * @param payload - The upfront payment payload
   * @returns The workflow ID and run ID
   */
  async createUpfrontPayment(
    payload: UpfrontPayment,
    log: Logger
  ): Promise<{ holdingTransactionId: string }> {
    count('payments_sponsored_promo_redesign_count');

    // NOTE: This would normally be injected via a dependency injection container, this
    // is a temporary step to enable this new code architecture.
    const workflowFactory = new WorkflowFactory(await getTemporalClient());
    const result = await workflowFactory.startUpfrontPayment(payload, log);

    return result;
  },

  /**
   * Finalise an upfront payment workflow
   * @param payload - The upfront payment complete payload
   * @returns The workflow state
   */
  async finaliseUpfrontPayment(payload: UpfrontPaymentComplete, log: Logger) {
    const workflowId = upfrontPaymentWorkflowDefinition.generateWorkflowId(
      payload.orderId
    );

    log.info({ payload, workflowId }, 'Finalise upfront payment');

    const temporal = await getTemporalClient();
    const handle = temporal.workflow.getHandle(workflowId);

    // Get the state of the workflow in order to calculate the difference.
    // We need to calculate this ahead of signalling the workflow in order to
    // know if we should return the additional transaction.
    const state = await handle.query(
      upfrontPaymentWorkflowDefinition.queries.getState
    );
    const difference = payload.finalAmountCents - state.holdingAmountCents;

    await handle.signal(upfrontPaymentWorkflowDefinition.signals.finalised, {
      ...payload,
      differenceCents: difference,
      type: 'finalised',
    });

    log.info({ state }, 'Upfront payment finalised workflow initiated');
  },

  /**
   * Get the status of an upfront payment workflow
   * @param orderId - The order ID
   * @returns The upfront payment status
   */
  async getUpfrontPaymentStatus(
    orderId: string
  ): Promise<{ flowStep: UpfrontFlowStep }> {
    const workflowId =
      upfrontPaymentWorkflowDefinition.generateWorkflowId(orderId);
    const temporal = await getTemporalClient();
    const handle = temporal.workflow.getHandle(workflowId);

    const state = await handle.query(
      upfrontPaymentWorkflowDefinition.queries.getState
    );

    return {
      flowStep: state.flowStep ?? 'unknown',
    };
  },

  /**
   * Cancel an upfront payment workflow and revert held funds
   * @param payload - The cancel upfront payment payload
   */
  async cancelAndRevertUpfrontPayment(
    payload: CancelUpfrontPayment
  ): Promise<void> {
    logger.info({ payload }, 'Cancel upfront order and revert held funds');

    const workflowId = upfrontPaymentWorkflowDefinition.generateWorkflowId(
      payload.orderId
    );
    const temporal = await getTemporalClient();
    const handle = temporal.workflow.getHandle(workflowId);

    if (!handle) {
      throw new Error('Workflow not found');
    }

    await handle.signal(upfrontPaymentWorkflowDefinition.signals.cancelled, {
      reason: 'User cancelled the order',
      type: 'cancelled',
    });
  },
};
