import assert from 'node:assert';
import Logger from 'bunyan';
import { createClient, ItemRequestBody } from 'zai-payments';
import {
  DisburseFromWalletToBankAccount,
  GatewayFundTransfer,
  GetOrCreatePaymentBody,
} from '../../../common/types/gateway.types';
import { PAYMENT_TYPES } from '../../../../lib/backends/promisepay/types';
import { CurrencyCodes } from '../../../../types/currency_codes';
import { histogramFn, increment } from '../../../../lib/stats';

export type ZaiClient = ReturnType<typeof createClient>;

/**
 * The Zai / PromisePay payment gateway implementation.
 */
export class ZaiFundTransferGateway implements GatewayFundTransfer {
  constructor(
    private readonly logger: Logger,
    private readonly zai: ZaiClient
  ) {
    // Constructor
  }

  /**
   * Get an item from Zai by ID
   * Reference: https://developer.hellozai.com/v2.0/reference/showitem
   * @param id - The ID of the item to retrieve
   */
  async getItem(id: string) {
    this.logger.info({ id }, 'Zai gateway get item');

    return histogramFn('zai_gateway_get_item', async () => {
      const item = await this.zai.items
        .showItem(id)
        .then(res => res.items)
        .catch(err => {
          this.logger.error({ err, id }, 'Zai gateway get item failed');
          throw err;
        });

      increment('zai_gateway_get_item_succeeded');

      return item;
    });
  }

  /**
   * Create an item in Zai to track the required movement of funds between the buyer and seller
   * Reference: https://developer.hellozai.com/v2.0/reference/createitem
   * @param payload
   */
  async getOrCreateItem(payload: GetOrCreatePaymentBody): Promise<string> {
    this.logger.info({ payload }, 'Zai gateway get or create item');

    return histogramFn('zai_gateway_get_or_create_item', async () => {
      const existingItem = await this.zai.items
        .showItem(payload.id)
        .then(res => res.items)
        .catch(err => {
          // 422 in this case means the item does not exist
          if (err?.response?.status !== 422) {
            this.logger.error(
              { err, request: payload, response: err.response.data },
              'Zai gateway show item failed'
            );
            throw err;
          }
          return null;
        });

      if (existingItem?.id) {
        this.logger.info({ id: payload.id }, 'Zai item already exists');

        return existingItem.id;
      }

      const request: ItemRequestBody = {
        id: payload.id,
        name: payload.name,
        amount: payload.amount,
        currency: payload.currency ?? CurrencyCodes.AUD,
        payment_type: PAYMENT_TYPES.EXPRESS,
        buyer_id: payload.externalBuyerId,
        seller_id: payload.externalSellerId,
        fee_ids: payload.feeIds,
        description: payload.description,
        tax_invoice: payload.taxInvoice,
        custom_descriptor: payload.customDescriptor,
      };

      const item = await this.zai.items
        .createItem(request)
        .then(res => res.items)
        .catch(err => {
          this.logger.error({ err, request }, 'Zai gateway create item failed');
          throw err;
        });

      if (!item?.id) {
        this.logger.error({ item, request }, 'Zai item not created');

        throw new Error('Zai item not created');
      }

      return item.id;
    });
  }

  /**
   * Make a payment against a previously created gateway item by
   * transferring funders from the buyer's bank account into a wallet
   * (typically the seller's)
   * @param itemId - The ID of the item to make a payment against.
   */
  async makePayment(
    itemId: string,
    accountId: string,
    mobileNumber: string
  ): Promise<void> {
    assert(itemId, 'Item ID is required');
    assert(mobileNumber, 'Mobile number is required');

    return histogramFn('zai_gateway_make_payment', async () => {
      // Create a payment against the item
      // Reference: https://developer.hellozai.com/v2.0/reference/patch_items-id-make-payment
      const request = {
        account_id: accountId,
        merchant_phone: mobileNumber,
      };

      const payment = await this.zai.items
        .makePayment(itemId, request)
        .catch(err => {
          this.logger.error({ request, err, itemId }, 'Zai payment failed');
          throw err;
        });

      this.logger.info({ payment }, 'Zai payment created');
    });
  }

  /**
   * Refund a partial amount of a payment.
   * @param itemId - The ID of the item to refund.
   * @param refundAmountCents
   */
  async partialRefund(itemId: string, refundAmountCents: number) {
    return histogramFn('zai_gateway_partial_refund', async () => {
      const refund = await this.zai.items.refund(itemId, {
        refund_amount: refundAmountCents,
      });

      this.logger.info({ refund }, 'Zai partial refund created');
    });
  }

  /**
   * Disburse funds to a bank account.
   * @param payload
   */
  async disburse(payload: DisburseFromWalletToBankAccount): Promise<void> {
    return histogramFn('zai_gateway_disburse', async () => {
      const wallet = payload.fromWallet;
      const { amount } = payload;
      const bankAccount = payload.toBankAccount;

      if (amount > wallet.balance) {
        this.logger.error(
          { amount, walletBalance: wallet.balance },
          'Zai Invalid wallet balance'
        );

        throw new Error('Invalid amount');
      }

      this.logger.info(
        { amount, walletBalance: wallet.balance },
        'Zai disburse funds to bank account'
      );

      // https://developer.hellozai.com/v2.0/reference/withdrawfunds
      const disbursement = await this.zai.walletAccounts
        .withdrawFunds(wallet.accountId, {
          amount: payload.amount,
          custom_descriptor: payload.descriptor,
          account_id: bankAccount.bankAccountId,
        })
        .then(res => res.disbursements)
        .catch(err => {
          this.logger.error({ err, payload }, 'Zai disbursement failed');
          throw err;
        });

      if (!disbursement?.id) {
        this.logger.error({ disbursement }, 'Zai disbursement failed');

        throw new Error('Zai disbursement failed');
      }
    });
  }
}
