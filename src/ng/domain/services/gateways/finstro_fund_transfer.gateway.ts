import Logger from 'bunyan';
import {
  GatewayFundTransfer,
  DisburseFromWalletToBankAccount,
  GetOrCreatePaymentBody,
} from '../../../common/types/gateway.types';

/**
 * This is a gateway class that is responsible for handling all the
 * fund transfer related operations
 */
export class FinstroFundTransferGateway implements GatewayFundTransfer {
  constructor(private readonly logger: Logger) {
    // Constructor
  }

  getOrCreateItem(payload: GetOrCreatePaymentBody): Promise<string> {
    this.logger.trace({ payload }, 'Placeholder: Making payment');

    return Promise.resolve('123');
  }

  makePayment(
    itemId: string,
    accountId: string,
    mobileNumber: string
  ): Promise<void> {
    this.logger.trace(
      { itemId, accountId, mobileNumber },
      'Placeholder: Making payment'
    );

    return Promise.resolve();
  }

  disburse(payload: DisburseFromWalletToBankAccount): Promise<void> {
    this.logger.trace(
      { payload },
      'Placeholder: Disburse funds to bank account'
    );

    return Promise.resolve();
  }
}
