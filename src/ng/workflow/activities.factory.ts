import { getTemporalClient } from '@ordermentum/temporal';
import { createFundingActivities } from './activities/wallet.activities';
import { PaymentMethodService } from '../domain/services/domain/payment_method.service';
import { logger } from '../../lib/logger';
import { TransactionsRepository } from '../domain/repositories/transactions.repository';
import { UserRepository } from '../domain/repositories/user.repository';
import { createUpfrontControlActivities } from './activities/upfront_control.activities';
import { UserService } from '../domain/services/domain/user.service';
import { ZaiFundTransferGateway } from '../domain/services/gateways/zai_fund_transfer.gateway';
import { TransactionService } from '../domain/services/domain/transaction.service';
import { createPaymentActivities } from './activities/payment.activities';
import { ZaiWalletsGateway } from '../domain/services/gateways/zai_wallets.gateway';
import { createPromoActivities } from './activities/promotional_payment.activities';
import { WorkflowFactory } from './workflow.factory';

export const createActivities = (
  transactionService: TransactionService,
  transactionsRepository: TransactionsRepository,
  zaiFundTransferGateway: ZaiFundTransferGateway,
  zaiWalletsGateway: ZaiWalletsGateway,
  userService: UserService,
  userRepository: UserRepository,
  paymentMethodService: PaymentMethodService,
  temporalClient: Awaited<ReturnType<typeof getTemporalClient>>
) => {
  const userActivities = createFundingActivities(
    transactionsRepository,
    transactionService,
    zaiFundTransferGateway,
    zaiWalletsGateway,
    userRepository,
    paymentMethodService,
    logger
  );

  const upfrontControlActivities = createUpfrontControlActivities(
    userService,
    transactionService
  );

  const paymentActivities = createPaymentActivities(
    transactionsRepository,
    transactionService,
    userRepository,
    zaiFundTransferGateway,
    logger
  );

  const workflowFactory = new WorkflowFactory(temporalClient);

  const promos = createPromoActivities(transactionService, workflowFactory);

  return {
    ...userActivities,
    ...upfrontControlActivities,

    // TODO: Remove this once we have a proper upfront control workflow
    // These provide backwards compatibility with the old upfront control workflow
    // for inflight upfront payments in temporal, they can be removed!
    // skipcq: JS-0387
    start: upfrontControlActivities.upfrontStart,

    holdingTransactionSucceeded:
      // skipcq: JS-0387
      upfrontControlActivities.upfrontHoldingTransactionSucceeded,
    holdingTransactionFailed:
      // skipcq: JS-0387
      upfrontControlActivities.upfrontHoldingTransactionFailed,
    finaliseSucceeded:
      // skipcq: JS-0387
      upfrontControlActivities.upfrontFinaliseSucceeded,
    finaliseFailed:
      // skipcq: JS-0387
      upfrontControlActivities.upfrontFinaliseFailed,
    finaliseCancelled:
      // skipcq: JS-0387
      upfrontControlActivities.upfrontFinaliseCancelled,

    // TODO: Remove this once no workflow is using it
    partialRefundTransaction: userActivities.refundTransaction,

    ...paymentActivities,
    ...promos,
  };
};

export type WorkflowActivities = ReturnType<typeof createActivities>;
