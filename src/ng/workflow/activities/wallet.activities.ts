import assert from 'node:assert';
import Logger from 'bunyan';
import { ApplicationFailure } from '@temporalio/common';
import { ZaiFundTransferGateway } from '../../domain/services/gateways/zai_fund_transfer.gateway';
import { PaymentMethodService } from '../../domain/services/domain/payment_method.service';
import { UserRepository } from '../../domain/repositories/user.repository';
import {
  Backend,
  states,
  TransactionAttributes,
} from '../../../models/transaction';
import { CurrencyCodes } from '../../common/types/currency.types';
import { journalv3 } from '../../../lib/journal_v3/journal_operations';
import { TransactionService } from '../../domain/services/domain/transaction.service';
import { CreateTransactionAttrs } from '../../../types/payment_types';
import { ZaiWalletsGateway } from '../../domain/services/gateways/zai_wallets.gateway';
import { TransactionsRepository } from '../../domain/repositories/transactions.repository';
import { toWorkflowError } from '../../common/helpers/errors.helpers';
import { getProviderErrorDetails } from '../../../lib/backends/promisepay/errors';
import { WalletTransferParams } from '../types/wallet_transfer.types';
import { PaymentMethodTransferParams } from '../types/payment_methods_transfer.types';

/**
 * Create activities for holding funds in escrow.
 *
 * @param transactionsRepository - The repository for transactions.
 * @param zaiFundTransferGateway - The gateway for Zai fund transfers.
 * @param userRepository - The repository for users.
 * @param paymentMethodService - The service for payment methods.
 * @param logger - The logger.
 * @returns The activities.
 */
export function createFundingActivities(
  transactionsRepository: TransactionsRepository,
  transactionService: TransactionService,
  zaiFundTransferGateway: ZaiFundTransferGateway,
  zaiWalletsGateway: ZaiWalletsGateway,
  userRepository: UserRepository,
  paymentMethodService: PaymentMethodService,
  logger: Logger
) {
  return {
    /**
     * Transfer funds from the buyer's payment method to the seller's wallet to
     * hold in escrow until the order is finalised.
     */
    async paymentMethodTransfer(payload: PaymentMethodTransferParams) {
      const sender = await userRepository.getUserById(payload.buyerId);
      const recipient = await userRepository.getUserById(payload.sellerId);

      assert(sender, 'Sender not found');
      assert(recipient, 'Recipient not found');

      const methods =
        await paymentMethodService.selectUpfrontHoldingPaymentMethod(
          sender.id,
          payload.paymentMethodId,
          false
        );

      if (!methods.cardPaymentMethod && !methods.paymentMethod) {
        logger.error({ payload }, 'No matching payment method found');
        throw ApplicationFailure.nonRetryable('Payment method not found');
      }

      return journalv3.userSale(
        async () => {
          const transactionAttrs: TransactionAttributes = {
            amount: payload.amountCents,
            id: payload.transactionId,
            buyer_id: payload.buyerId,
            seller_id: payload.sellerId,
            card_payment_method_id: methods.cardPaymentMethod?.id,
            bank_payment_method_id: undefined,
            wallet_payment_method_id: undefined,
            paymentMethodId: methods.paymentMethod?.id,
            currency: payload.currencyCode ?? CurrencyCodes.AUD,
            description: payload.description,
            state: states.pending,
            backend: methods.paymentMethod?.backend ?? sender.backend,
            name: payload.description,
            transactionType: 'capture',
            orderIds: payload.orderIds,
            invoiceIds: payload.invoiceIds,
            relatedTransactions: payload.relatedTransactions,
            context: {
              ...payload.context,
              isZaiAsync: false,

              // Leave undefined so legacy workflow handlers are not triggered
              workflow: undefined,
              immediate: undefined,
              backend: sender.backend,
            },
          };

          // Get the payment account ID from the transaction details
          // Note that we do not load the users wallets as they are not
          // valid for this type of payment.
          const backendId = await paymentMethodService.getBackendAccountId(
            sender.id,
            transactionAttrs
          );

          assert(backendId, 'Payment method not found');

          await transactionService
            .getOrCreateTransaction(recipient, transactionAttrs, 'apply-fee')
            .catch(err => {
              logger.error({ err }, 'Failed to create transaction');
              throw toWorkflowError(err, 'Failed to create transaction');
            });

          const itemId = await zaiFundTransferGateway
            .getOrCreateItem({
              id: payload.transactionId,
              amount: payload.amountCents,
              name: payload.buyerText,
              description: payload.buyerText,
              currency: payload.currencyCode,
              externalBuyerId: sender.external_id,
              externalSellerId: recipient.external_id,
            })
            .catch(err => {
              const { message } = getProviderErrorDetails(err);
              throw toWorkflowError(err, message);
            });

          // IMPORTANT!
          // This is the last step in the activity and IS NOT idempotent.
          // If further steps are required, they should be added to another activity
          await zaiFundTransferGateway
            .makePayment(itemId, backendId, sender.mobile_number)
            .catch(err => {
              const { message } = getProviderErrorDetails(err);
              throw toWorkflowError(err, message);
            });

          return {
            returned: {},
            details: {
              buyerId: sender.id,
              sellerId: recipient.id,
              totalCents: payload.amountCents,
              fundedByPrinciple: false,
              walletFundedCents: 0,
              walletFundTxId: undefined,
              feeCents: 0,
              sellerText: payload.sellerText,
              buyerText: payload.buyerText,
              purchaseTxId: payload.transactionId,
              purchaseTxState: states.completed,
            },
          };
        },
        undefined,
        logger
      );
    },

    /**
     * Transfer funds from the buyer's wallet to the seller's wallet to
     * hold in escrow until the order is finalised.
     * @param payload - The payload for the activity.
     */
    async walletTransfer(payload: WalletTransferParams) {
      const sender = await userRepository.getUserById(payload.senderId);
      const recipient = await userRepository.getUserById(payload.recipientId);

      assert(sender, 'Sender not found');
      assert(recipient, 'Recipient not found');

      await journalv3.userSale(
        async () => {
          const senderWallet = await zaiWalletsGateway
            .getUserWallet(sender.id)
            .catch(err => {
              throw toWorkflowError(err, 'Failed to get sender wallet');
            });

          const transactionAttrs: CreateTransactionAttrs = {
            amount: payload.amountCents,
            id: payload.transactionId,
            buyer_id: payload.senderId,
            seller_id: payload.recipientId,
            card_payment_method_id: undefined,
            bank_payment_method_id: undefined,
            paymentMethodId: undefined,
            wallet_payment_method_id: senderWallet.accountId,
            currency: payload.currencyCode ?? CurrencyCodes.AUD,
            description: payload.description,
            state: states.pending,
            backend: Backend.PROMISEPAY,
            name: payload.description,
            transactionType: payload.transactionType,
            orderIds: payload.orderIds,
            invoiceIds: payload.invoiceIds,
            useWalletAccount: true,
            relatedTransactions: payload.relatedTransactions,
            context: {
              isZaiAsync: false,

              // Leave undefined so legacy workflow handlers are not triggered
              workflow: undefined,
              immediate: undefined,
              backend: Backend.PROMISEPAY,
            },
          };

          await transactionService
            .getOrCreateTransaction(recipient, transactionAttrs, 'no-fee')
            .catch(err => {
              throw toWorkflowError(err, 'Failed to create transaction');
            });

          const itemId = await zaiFundTransferGateway
            .getOrCreateItem({
              id: payload.transactionId,
              amount: payload.amountCents,
              name: payload.senderText,
              description: payload.senderText,
              currency: payload.currencyCode ?? CurrencyCodes.AUD,
              externalBuyerId: sender.external_id,
              externalSellerId: recipient.external_id,
            })
            .catch(err => {
              throw toWorkflowError(err, 'Failed to get or create Zai item');
            });

          // IMPORTANT!
          // This is the last step in the activity and IS NOT idempotent.
          // If further steps are required, they should be added to another activity
          await zaiFundTransferGateway
            .makePayment(itemId, senderWallet.accountId, sender.mobile_number)
            .catch(err => {
              throw toWorkflowError(
                err,
                'Failed to make wallet to wallet payment'
              );
            });

          return {
            returned: {},
            details: {
              buyerId: sender.id,
              sellerId: recipient.id,
              totalCents: payload.amountCents,
              fundedByPrinciple: false,
              walletFundedCents: 0,
              walletFundTxId: undefined,
              feeCents: 0,
              sellerText: payload.recipientText,
              buyerText: payload.senderText,
              purchaseTxId: undefined,
              purchaseTxState: states.completed,
            },
          };
        },
        undefined,
        logger
      );
    },

    /**
     * Ensure that the sponsored promo wallet has sufficient balance to cover the
     * promo amount.
     *
     * @param promo - The sponsored promo workflow arguments.
     * @returns True if the balance is sufficient, false otherwise.
     */
    async walletCheckBalance(args: {
      walletId: string;
      requiredAmount: number;
    }): Promise<boolean> {
      const wallet = await zaiWalletsGateway.getUserWallet(args.walletId);

      return args.requiredAmount <= wallet.balance;
    },

    /**
     * Refund a partial amount of a transaction. The transaction (Zai item) must already
     * exist and the amount cannot exceed the original transaction amount.
     * @param payload - The payload for the activity.
     */
    async refundTransaction(payload: {
      transactionId: string;
      refundAmountCents: number;
    }) {
      logger.info({ payload }, 'Validating transaction for refund');

      await journalv3.userRefund(
        async () => {
          const transaction = await transactionsRepository.getTransactionById(
            payload.transactionId
          );

          assert(
            transaction,
            `Refund transaction ${payload.transactionId} not found`
          );

          const providerItem = await zaiFundTransferGateway.getItem(
            transaction.id
          );

          logger.info({ providerItem }, 'Provider item');

          assert(providerItem?.state, 'Provider item not found');

          if (
            ['refund_pending', 'refunded'].includes(providerItem.state) ||
            providerItem.refunded_amount === payload.refundAmountCents
          ) {
            logger.info(
              { providerItem },
              'Transaction is already refunded or refund amount is the same as the remaining amount'
            );
            return {
              returned: {},
              details: {
                refundedToId: transaction.buyer_id,
                refundedFromId: transaction.seller_id,
                amountCents: payload.refundAmountCents,
                refundedFromText: transaction.name,
                refundedToText: transaction.name,
                refundTxId: transaction.id,
              },
            };
          }

          assert(
            providerItem.net_amount,
            'No remaining amount found on provider item'
          );

          assert(
            payload.refundAmountCents <= providerItem.net_amount,
            'Refund amount exceeds remaining amount of provider item'
          );

          logger.info(
            { providerItem },
            'Refunding transaction with remaining amount'
          );

          await zaiFundTransferGateway.partialRefund(
            payload.transactionId,
            payload.refundAmountCents
          );

          return {
            returned: {},
            details: {
              refundedToId: transaction.buyer_id,
              refundedFromId: transaction.seller_id,
              amountCents: payload.refundAmountCents,
              refundedFromText: transaction.name,
              refundedToText: transaction.name,
              refundTxId: transaction.id,
            },
          };
        },
        undefined,
        logger
      );
    },

    /**
     * Set the release timestamp for a transaction id to now
     * so the background job will release the funds to the seller, additionally
     * set the settlement amount must be set in order for the disbursement
     * query to release the funds to the seller.
     * @param payload - The payload for the activity.
     */
    async transactionsReleaseNow(payload: {
      transactionId: string;
      sellerId: string;
      settlementAmountCents: number;
    }) {
      await transactionService.transactionsReleaseNow({
        transactionId: payload.transactionId,
        sellerId: payload.sellerId,
        settlementAmountCents: payload.settlementAmountCents,
      });
    },
  };
}
