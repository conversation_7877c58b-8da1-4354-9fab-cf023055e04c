import { proxyActivities } from '@temporalio/workflow';
import { WorkflowDefinition } from '@ordermentum/temporal';
import type { WorkflowActivities } from '../activities.factory';
import { getActivityRetryPolicy } from '../../common/helpers/temporal.helpers';
import {
  getPromoTakePaymentArgs,
  getPromoWalletFundingArgs,
} from '../factories/promotional_payment.factory';
import { PromotionalPaymentWorkflowArgs } from '../types/promotional_payment.types';

export const DEFAULT_START_TO_CLOSE_TIMEOUT = '60 seconds';

/**
 * The activities for the promotional payment workflow.
 */
const activities = proxyActivities<WorkflowActivities>({
  startToCloseTimeout: DEFAULT_START_TO_CLOSE_TIMEOUT,
  retry: getActivityRetryPolicy(),
});

/**
 * This workflow is responsible for handling the payment for a promotional payment.
 * The promotional payment workflow works by:
 *
 * 1. Ensuring the principle promo wallet has sufficient funds
 * 2. Transfer the promo amount from the principle promo funding wallet to the seller's wallet
 * 3. Take payment from the venue to the supplier's wallet
 * 4. Mark the transactions as succeeded & call back to the OM service
 * 5. If the workflow fails, return the promo funds & mark the transactions as failed & call back to the OM service
 *
 * Original requirements: https://ordermentum.atlassian.net/browse/C1-1887
 */
async function workflow(args: PromotionalPaymentWorkflowArgs) {
  let promoFundsTransferred = false;

  try {
    // Step 1: Make sure there is enough money in the promotional wallet to cover the promo amount.
    await activities.walletCheckBalance({
      walletId: args.fundingWalletId,
      requiredAmount: args.promoAmountCents,
    });

    // Step 2: Move the funds from the promotional wallet to the seller's wallet.
    // We only do this if the promotional wallet has sufficient funds.
    await activities.walletTransfer(getPromoWalletFundingArgs(args));
    promoFundsTransferred = true;

    // Step 3: Take payment from the venue to the supplier's wallet
    await activities.paymentMethodTransfer(getPromoTakePaymentArgs(args));

    // Step 4: Mark the transactions as succeeded & call back to the OM service
    await activities.promoSucceeded(args);
  } catch (err) {
    // Error handling step 1: Return the promo funds if they were transferred
    if (promoFundsTransferred) {
      await activities.promoReturnSponsoredFunds(args);
    }

    // Error handling step 2: Mark the transactions as failed & call back to the OM service
    await activities.promoFailed(args);

    throw err;
  }
}

/**
 *
 */
export const promotionalPaymentWorkflowDefinition: WorkflowDefinition<
  [PromotionalPaymentWorkflowArgs]
> = {
  name: 'promotionalPaymentWorkflow',
  workflow,
  queueName: 'payments:promotional-payments',
  generateWorkflowId: (identifier: string) =>
    `promotional-payment-${identifier}`,
  queries: {},
  signals: {},
  updates: {},
  path: __filename,
};
