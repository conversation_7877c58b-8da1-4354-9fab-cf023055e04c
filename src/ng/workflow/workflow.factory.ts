import { getTemporalClient } from '@ordermentum/temporal';
import config from 'config';
import Logger from 'bunyan';
import { AccountIdRequestBody, AsyncMakePaymentBody } from 'zai-payments';
import { startChild, uuid4 } from '@temporalio/workflow';
import { makeZaiPaymentWorkflowDefinition } from './definitions/make_zai_payment.workflow';
import { getWorkflowRetryPolicy } from '../common/helpers/temporal.helpers';
import { SYSTEM_ERROR } from '../../lib/backends/promisepay/errors';
import { promotionalPaymentWorkflowDefinition } from './definitions/promotional_payment.workflow';
import { upfrontPaymentWorkflowDefinition } from './definitions/upfront_payment.workflow';
import { UpfrontPayment } from '../common/requests/upfront_payments.request';
import {
  PromotionalPaymentRequest,
  PromotionalPaymentRequestSchema,
} from './types/promotional_payment.types';
import { WalletTransferParams } from './types/wallet_transfer.types';
import { walletTransferWorkflowDefinition } from './definitions/wallet_transfer.workflow';

const OM_FUNDING_USER_ID = config.get<string>('OM_FUNDING_USER_ID');

/**
 *
 */
export class WorkflowFactory {
  constructor(
    private readonly temporal: Awaited<ReturnType<typeof getTemporalClient>>
  ) {
    // Constructor
  }

  /**
   * Starts a workflow to make a Zai payment.
   *
   * @param id - The ID of the transaction to make a Zai payment for.
   * @param requestBody - The request body for the Zai payment.
   * @param log - The logger to use for logging.
   * @returns A promise that resolves with the result of the Zai payment.
   */
  async startMakeZaiPayment(
    id: string,
    requestBody: AccountIdRequestBody,
    log: Logger
  ): Promise<AsyncMakePaymentBody> {
    const workflowInitiationTime = Date.now();
    log.info('Performing async payment for a user with workflow enabled');
    const workflowId = makeZaiPaymentWorkflowDefinition.generateWorkflowId(id);
    log.info(
      { workflowId, transactionId: id, requestBody },
      'Starting async payment workflow'
    );
    try {
      await this.temporal.workflow.start(
        makeZaiPaymentWorkflowDefinition.workflow,
        {
          args: [
            {
              transactionId: id,
              body: requestBody,
              releasePolicy: 'immediate',
              workflowInitiationTime,
            },
          ],
          taskQueue: makeZaiPaymentWorkflowDefinition.queueName,
          retry: getWorkflowRetryPolicy(),
          workflowId,
          workflowIdConflictPolicy: 'FAIL',
          workflowIdReusePolicy: 'REJECT_DUPLICATE',
        }
      );
      /**
       * Note: Workflow will take care of calling the make payment endpoint
       * We need to return a valid AsyncMakePaymentBody object
       * so that we can update the transaction context
       */
      return {
        last_updated: new Date().toISOString(),
      } as AsyncMakePaymentBody;
    } catch (error) {
      log.error({ error }, 'Failed to start async payment workflow');
      throw SYSTEM_ERROR;
    }
  }

  /**
   * Starts a workflow to make a promotional payment.
   *
   * @param request - The request body for the promotional payment.
   * @param log - The logger to use for logging.
   * @returns A promise that resolves with the result of the promotional payment.
   */
  // skipcq: JS-0105
  async makePromotionalPayment(
    request: PromotionalPaymentRequest,
    log: Logger
  ): Promise<{
    purchaseTransactionId: string;
    promoTransactionId: string;
  }> {
    // Validate the request
    const validatedRequest = PromotionalPaymentRequestSchema.parse(request);

    log.info('Performing promotional payment for a user with workflow enabled');

    const purchaseTransactionId = uuid4();
    const promoTransactionId = uuid4();

    const workflowId = promotionalPaymentWorkflowDefinition.generateWorkflowId(
      purchaseTransactionId
    );

    log.info(
      { workflowId, purchaseTransactionId, promoTransactionId, request },
      'Starting promotional payment workflow'
    );

    try {
      const temporal = await getTemporalClient();
      await temporal.workflow.start(
        promotionalPaymentWorkflowDefinition.workflow,
        {
          args: [
            {
              ...validatedRequest,
              purchaseTransactionId,
              promoTransactionId,
              fundingWalletId: OM_FUNDING_USER_ID,
            },
          ],
          taskQueue: promotionalPaymentWorkflowDefinition.queueName,
          retry: getWorkflowRetryPolicy(),
          workflowId,
          workflowIdConflictPolicy: 'FAIL',
          workflowIdReusePolicy: 'REJECT_DUPLICATE',
        }
      );

      return {
        promoTransactionId,
        purchaseTransactionId,
      };
    } catch (error) {
      log.error({ error }, 'Failed to start promotional payment workflow');
      throw SYSTEM_ERROR;
    }
  }

  /**
   * Starts a child workflow to transfer funds from one wallet to another.
   * @param args - The arguments for the wallet transfer.
   */
  // skipcq: JS-0105
  async startChildWalletTransfer(args: WalletTransferParams) {
    const workflowId = walletTransferWorkflowDefinition.generateWorkflowId(
      args.transactionId
    );

    await startChild(walletTransferWorkflowDefinition.workflow, {
      workflowId,
      args: [args],
      parentClosePolicy: 'ABANDON',
      searchAttributes: {
        CustomKeywordField: [args.orderIds[0]],
      },
    });
  }

  /**
   * Starts an upfront payment workflow.
   *
   * @param payload - The payload for the upfront payment.
   * @param log - The logger to use for logging.
   * @returns A promise that resolves with the holding transaction ID.
   */
  // skipcq: JS-0105
  async startUpfrontPayment(
    payload: UpfrontPayment,
    log: Logger
  ): Promise<{
    holdingTransactionId: string;
  }> {
    const workflowId = upfrontPaymentWorkflowDefinition.generateWorkflowId(
      payload.orderId
    );
    const holdingTransactionId = uuid4();

    log.info(
      { workflowId, holdingTransactionId, payload },
      'Initiating upfront payment workflow'
    );

    const temporal = await getTemporalClient();
    log.info(
      {
        workflowId,
        taskQueue: upfrontPaymentWorkflowDefinition.queueName,
      },
      'Starting temporal workflow'
    );

    await temporal.workflow.start(upfrontPaymentWorkflowDefinition.workflow, {
      args: [payload, holdingTransactionId],
      taskQueue: upfrontPaymentWorkflowDefinition.queueName,
      retry: getWorkflowRetryPolicy(),
      workflowId,
      workflowIdConflictPolicy: 'FAIL',
      workflowIdReusePolicy: 'REJECT_DUPLICATE',

      // Start with a delay to (in a hacky way) ensure that the order has
      // committed to the database in the OM service before we potentially
      // call back with /:id/upfront/holding-finished. This attempts to
      // mitigate a very low chance edge case.
      startDelay: '2s',
      searchAttributes: {
        CustomKeywordField: [payload.orderId],
      },
    });

    return { holdingTransactionId };
  }
}
