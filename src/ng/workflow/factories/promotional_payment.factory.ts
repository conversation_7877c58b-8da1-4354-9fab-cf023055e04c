import { PaymentMethodTransferParams } from '../types/payment_methods_transfer.types';
import { PromotionalPaymentWorkflowArgs } from '../types/promotional_payment.types';
import { WalletTransferParams } from '../types/wallet_transfer.types';

/**
 * This factory function creates arguments for the call to
 * transfer funds from the sponsored promo wallet to the seller's wallet.
 */
export function getPromoWalletFundingArgs(
  args: PromotionalPaymentWorkflowArgs
): WalletTransferParams {
  return {
    description: 'Sponsored promo payment',
    amountCents: args.promoAmountCents,
    senderId: args.fundingWalletId,
    senderText: 'Sponsored promo payment',
    recipientId: args.recipientId,
    recipientText: 'Sponsored promo payment',
    orderIds: args.orderIds,
    invoiceIds: args.invoiceIds,
    transactionId: args.promoTransactionId,
    transactionType: 'capture',
    relatedTransactions: [args.purchaseTransactionId],
  };
}

/**
 * This factory function creates arguments for the call to
 * take payment from the venue's configured payment method to the
 * supplier's wallet.
 */
export function getPromoTakePaymentArgs(
  args: PromotionalPaymentWorkflowArgs
): PaymentMethodTransferParams {
  return {
    amountCents: args.purchaseAmountCents - args.promoAmountCents,
    buyerId: args.senderId,
    buyerText: args.description,
    description: args.description,
    paymentMethodId: args.senderPaymentMethodId,
    sellerId: args.recipientId,
    sellerText: args.description,
    orderIds: args.orderIds,
    invoiceIds: args.invoiceIds,
    transactionId: args.purchaseTransactionId,
    relatedTransactions: [args.promoTransactionId],
  };
}

/**
 * This factory function creates arguments for the call to
 * reverse the funds from the seller's wallet to the sponsored promo wallet.
 */
export function getPromoWalletReversalArgs(
  args: PromotionalPaymentWorkflowArgs
): WalletTransferParams {
  return {
    description: 'Sponsored promo payment',
    amountCents: args.promoAmountCents,
    senderId: args.recipientId,
    senderText: 'Sponsored promo payment',
    recipientId: args.fundingWalletId,
    recipientText: 'Sponsored promo payment',
    orderIds: args.orderIds,
    invoiceIds: args.invoiceIds,
    transactionId: args.purchaseTransactionId,
    relatedTransactions: [args.promoTransactionId],
    transactionType: 'capture',
  };
}
