/**
 * TODO: These declarations are the first strawman attempts at having multiple
 * TODO: gateways in the platform. However, there are a number of questions that
 * TODO: still need to be answered...
 *
 *  - The users have a backend property, but what happens when transfers are
 *    happening across those boundaries
 *  - What are the use cases where backends do or do not support certain
 *    capabilities and how do we handle those cases?
 */

/**
 * The capabilities of a gateway.
 */
export enum GatewayCapabilities {
  PAYMENTS = 'PAYMENTS',
  USER_MANAGEMENT = 'USER_MANAGEMENT',
  CARD_MANAGEMENT = 'CARD_MANAGEMENT',
  BANK_MANAGEMENT = 'BANK_MANAGEMENT',
  WALLETS = 'WALLETS',
}

/**
 * An abstract representation of a wallet account.
 */
export type WalletAccount = {
  accountId: string;
  balance: number;
};

/**
 * An abstract representation of a gateway bank account.
 */
export type BankAccount = {
  bankAccountId: string;
};

/**
 * A request to create a payment transaction.
 */
export type GetOrCreatePaymentBody = {
  id: string;
  name: string;
  amount: number;
  currency?: string;
  externalBuyerId: string;
  externalSellerId: string;
  feeIds?: string;
  description?: string;
  taxInvoice?: boolean;
  customDescriptor?: string;
};

/**
 * A request to disburse funds to a bank account.
 */
export type DisburseFromWalletToBankAccount = {
  fromWallet: WalletAccount;
  toBankAccount: BankAccount;
  amount: number;
  descriptor: string;
};

/**
 * A collection of all supported gateways for a given backend.
 */
export interface Gateways {
  funds: GatewayFundTransfer | undefined;
  users: GatewayUserManagement | undefined;
  cards: GatewayCardManagement | undefined;
  banks: GatewayBankManagement | undefined;
  wallets: GatewayWallets | undefined;
}

/**
 * A gateway that is responsible for transferring funds between wallets and bank accounts.
 */
export interface GatewayFundTransfer {
  /**
   * Idempotently creates an item against the payment gateway. This tracks the basic information
   * about a movement of funds between two accounts.
   * @param payload
   */
  getOrCreateItem(payload: GetOrCreatePaymentBody): Promise<string>;

  /**
   * Makes a payment against a payment gateway item, which moves some of all of the funds
   * in order to settle the item. Once the item is settled, generally at this point the
   * gateways will not allow any further modifications to the item.
   * @param itemId
   * @param payload
   */
  makePayment(
    itemId: string,
    accountId: string,
    mobileNumber: string
  ): Promise<void>;

  /**
   * Disburses funds from a wallet to a bank account.
   * @param payload
   */
  disburse(payload: DisburseFromWalletToBankAccount): Promise<void>;
}

/**
 * A gateway that is responsible for managing cards.
 */
export interface GatewayCardManagement {
  /**
   * Create a payment card with the gateway.
   */
  createCard(): Promise<void>;

  /**
   * Update a payment card with the gateway.
   */
  updateCard(): Promise<void>;
}

/**
 * A gateway that is responsible for managing bank accounts.
 */
export interface GatewayBankManagement {
  getUserBankAccount(userId: string): Promise<BankAccount>;

  createBank(): Promise<void>;

  updateBank(): Promise<void>;
}

/**
 * A gateway that is responsible for managing wallets.
 */
export interface GatewayWallets {
  getUserWallet(userId: string): Promise<WalletAccount>;
}

export type GatewayUser = {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  mobile: string | undefined;
};

/**
 * A gateway that is responsible for managing users.
 */
export interface GatewayUserManagement {
  createUser(user: GatewayUser): Promise<void>;

  getUser(userId: string): Promise<GatewayUser | undefined>;
}
