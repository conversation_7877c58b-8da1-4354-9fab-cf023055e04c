/**
 * Journal creation operations.
 * These journal records will be marked as pending until they are
 * completed with an appropriate complete action call (see `complete
 * in this journal declaration below`)
 */
import { Transaction } from 'sequelize';
import Logger from 'bunyan';
import { entry, EntrySides, OperationStates } from '../../types/journal_types';
import { journaledExecutor } from './journal_executor';
import { JournalEntryV3Props } from '../../models/journal_v3_entry_model';
import {
  JournalUserRefundDetailsFn,
  JournalUserSaleDetailsFn,
} from '../../types/journal_operation_types';
import { toOperationState } from '../../models/transaction';

/**
 * Records the sale of something from one account holder to another.
 */
function userSale<Returned>(
  fn: JournalUserSaleDetailsFn<Returned>,
  tx: Transaction | undefined,
  log: Logger
): Promise<{
  journalId: string | undefined;
  returned: Returned;
}> {
  return journaledExecutor(
    async () => {
      //
      const { details, returned } = await fn(tx);
      const entries: JournalEntryV3Props[] = [];

      // If this transaction had to be funded, then note that
      // movement between buyer bank and OM
      if (details.walletFundedCents) {
        entries.push(
          entry.asset.holder.bank(
            details.buyerId,
            details.walletFundedCents,
            EntrySides.Debit,
            details.walletFundTxId,
            OperationStates.Succeeded
          ),
          entry.liability.wallet(
            details.buyerId,
            details.walletFundedCents,
            EntrySides.Credit,
            details.walletFundTxId,
            OperationStates.Succeeded
          )
        );
      }

      // Mark the sale information between the buyer and seller
      entries.push(
        entry.liability.wallet(
          details.buyerId,
          details.totalCents,
          EntrySides.Debit,
          details.purchaseTxId,
          toOperationState(details.purchaseTxState)
        ),
        entry.liability.wallet(
          details.sellerId,
          details.totalCents - details.feeCents,
          EntrySides.Credit,
          details.purchaseTxId,
          toOperationState(details.purchaseTxState)
        ),
        ...(details.feeCents
          ? [
              entry.revenue.fees(
                details.feeCents,
                EntrySides.Credit,
                details.purchaseTxId,
                toOperationState(details.purchaseTxState)
              ),
            ]
          : [])
      );

      // Pay the seller out, this notes the movement of money
      // held by OM after the payment that needs to move to the seller
      if (details.fundedByPrinciple) {
        entries.push(
          entry.liability.wallet(
            details.sellerId,
            details.totalCents,
            EntrySides.Debit,
            undefined,
            OperationStates.Succeeded
          ),
          entry.asset.holder.bank(
            details.sellerId,
            details.totalCents,
            EntrySides.Credit,
            undefined,
            OperationStates.Succeeded
          )
        );
      }

      return {
        record: {
          props: {
            senderText: details.buyerText,
            recipientText: details.sellerText,
            totalCents: details.totalCents,
          },
          entries,
        },
        returned,
      };
    },
    tx,
    log
  );
}

/**
 * Records the refunding of funds from one account holder to another.
 */
function userRefund<Returned>(
  fn: JournalUserRefundDetailsFn<Returned>,
  tx: Transaction | undefined,
  log: Logger
): Promise<{
  journalId: string | undefined;
  returned: Returned;
}> {
  return journaledExecutor(
    async () => {
      //
      const { details, returned } = await fn(tx);
      const entries: JournalEntryV3Props[] = [];

      // Mark the sale information between the buyer and seller
      entries.push(
        entry.liability.refund(
          details.refundedToId,
          details.amountCents,
          EntrySides.Debit,
          details.refundTxId,
          OperationStates.Succeeded
        ),
        entry.asset.holder.bank(
          details.refundedFromId,
          details.amountCents,
          EntrySides.Credit,
          details.refundTxId,
          OperationStates.Succeeded
        )
      );

      return {
        record: {
          props: {
            senderText: details.refundedFromText,
            recipientText: details.refundedToText,
            totalCents: details.amountCents,
          },
          entries,
        },
        returned,
      };
    },
    tx,
    log
  );
}

export const journalv3 = {
  userSale,
  userRefund,
};
