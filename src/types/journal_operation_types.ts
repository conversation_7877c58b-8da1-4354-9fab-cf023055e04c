import { Transaction } from 'sequelize';
import { states } from '../models/transaction';

/**
 * This holds information about the output of a user sale action.
 * This information is passed back to the journal operation helper (user sale) so it
 * has all the information to create the journal entries.
 *
 * TODO ARCHITECTURE: Eventually this will be flipped and this will be established upfront and passed into a workflow that moves the money
 */
export type UserSaleOutput = {
  /**
   * ID of the buyer
   */
  buyerId: string;

  /**
   * ID of the seller
   */
  sellerId: string;

  /**
   * Total amount of money for the transaction
   */
  totalCents: number;

  /**
   * If true then the transaction was funded by the business
   */
  fundedByPrinciple: boolean;

  /**
   * The amount of money (in cents) to top up the user's wallet from
   * their bank account through the configured payment gateway.
   */
  walletFundedCents: number;

  /**
   *
   */
  walletFundTxId: string | undefined;

  /**
   * The calculated fee that goes to Ordermentum - "clip of the ticket"
   */
  feeCents: number;

  /**
   * {string} Text to appear on the senders statement at most 40 characters
   */
  sellerText: string;

  /**
   * {string} Text to appear on the recipient's statement at most 40 characters
   */
  buyerText: string;

  /**
   * Identifier of the gateway transaction that transferred the
   * money for the sale
   */
  purchaseTxId: string | undefined;

  /**
   * Current state of the gateway transaction
   */
  purchaseTxState: states;
};

export type JournalUserSaleDetailsFn<Returned> = (
  tx: Transaction | undefined
) => Promise<{ returned: Returned; details: UserSaleOutput }>;

/**
 * This holds information about the output of a user sale action.
 * This information is passed back to the journal operation helper (user sale) so it
 * has all the information to create the journal entries.
 *
 * TODO ARCHITECTURE: Eventually this will be flipped and this will be established upfront and passed into a workflow that moves the money
 */
export type UserRefundOutput = {
  /**
   * ID of the user that is being refunded
   */
  refundedToId: string;

  /**
   * ID of the user that money is refunded from
   */
  refundedFromId: string;

  /**
   * Amount of money for the transaction
   */
  amountCents: number;

  /**
   * Text to appear on the senders statement at most 40 characters
   */
  refundedFromText: string;

  /**
   * Text to appear on the recipient's statement at most 40 characters
   */
  refundedToText: string;

  /**
   * Identifier of the gateway transaction that transferred the
   * money for the sale
   */
  refundTxId: string | undefined;
};

export type JournalUserRefundDetailsFn<Returned> = (
  tx: Transaction | undefined
) => Promise<{ returned: Returned; details: UserRefundOutput }>;
